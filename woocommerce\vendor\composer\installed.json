{"packages": [{"name": "automattic/jetpack-a8c-mc-stats", "version": "v3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-a8c-mc-stats.git", "reference": "d6bdf2f1d1941e0a22d17c6f3152097d8e0a30e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-a8c-mc-stats/zipball/d6bdf2f1d1941e0a22d17c6f3152097d8e0a30e6", "reference": "d6bdf2f1d1941e0a22d17c6f3152097d8e0a30e6", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.0.0", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-14T20:12:50+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-a8c-mc-stats", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-a8c-mc-stats/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Used to record internal usage stats for Automattic. Not visible to site owners.", "support": {"source": "https://github.com/Automattic/jetpack-a8c-mc-stats/tree/v3.0.0"}, "install-path": "../automattic/jetpack-a8c-mc-stats"}, {"name": "automattic/jetpack-admin-ui", "version": "v0.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-admin-ui.git", "reference": "a0894d34333451089add7b20f70e73b6509d6b6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-admin-ui/zipball/a0894d34333451089add7b20f70e73b6509d6b6d", "reference": "a0894d34333451089add7b20f70e73b6509d6b6d", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "automattic/jetpack-logo": "^3.0.0", "automattic/wordbless": "^0.4.2", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-25T16:33:45+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-admin-ui", "mirror-repo": "Automattic/jetpack-admin-ui", "branch-alias": {"dev-trunk": "0.5.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-admin-ui/compare/${old}...${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-admin-menu.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Generic Jetpack wp-admin UI elements", "support": {"source": "https://github.com/Automattic/jetpack-admin-ui/tree/v0.5.1"}, "install-path": "../automattic/jetpack-admin-ui"}, {"name": "automattic/jetpack-assets", "version": "v4.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-assets.git", "reference": "ca1ebeceeeafb31876a234fa68ea3065b3eab2c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-assets/zipball/ca1ebeceeeafb31876a234fa68ea3065b3eab2c3", "reference": "ca1ebeceeeafb31876a234fa68ea3065b3eab2c3", "shasum": ""}, "require": {"automattic/jetpack-constants": "^3.0.1", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "brain/monkey": "^2.6.2", "wikimedia/testing-access-wrapper": "^1.0 || ^2.0 || ^3.0", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-12-04T19:43:08+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-assets", "mirror-repo": "Automattic/jetpack-assets", "branch-alias": {"dev-trunk": "4.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-assets/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"files": ["actions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Asset management utilities for Jetpack ecosystem packages", "support": {"source": "https://github.com/Automattic/jetpack-assets/tree/v4.0.1"}, "install-path": "../automattic/jetpack-assets"}, {"name": "automattic/jetpack-autoloader", "version": "v5.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-autoloader.git", "reference": "eb6331a5c50a03afd9896ce012e66858de9c49c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-autoloader/zipball/eb6331a5c50a03afd9896ce012e66858de9c49c5", "reference": "eb6331a5c50a03afd9896ce012e66858de9c49c5", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "composer/composer": "^2.2", "yoast/phpunit-polyfills": "^1.1.1"}, "time": "2024-11-25T16:33:57+00:00", "type": "composer-plugin", "extra": {"class": "Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin", "autotagger": true, "mirror-repo": "Automattic/jetpack-autoloader", "branch-alias": {"dev-trunk": "5.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-autoloader/compare/v${old}...v${new}"}, "version-constants": {"::VERSION": "src/AutoloadGenerator.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Automattic\\Jetpack\\Autoloader\\": "src"}, "classmap": ["src/AutoloadGenerator.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Creates a custom autoloader for a plugin or theme.", "keywords": ["autoload", "autoloader", "composer", "jetpack", "plugin", "wordpress"], "support": {"source": "https://github.com/Automattic/jetpack-autoloader/tree/v5.0.0"}, "install-path": "../automattic/jetpack-autoloader"}, {"name": "automattic/jetpack-config", "version": "v3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-config.git", "reference": "fc719eff5073634b0c62793b05be913ca634e192"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-config/zipball/fc719eff5073634b0c62793b05be913ca634e192", "reference": "fc719eff5073634b0c62793b05be913ca634e192", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.0.0", "automattic/jetpack-connection": "@dev", "automattic/jetpack-import": "@dev", "automattic/jetpack-jitm": "@dev", "automattic/jetpack-post-list": "@dev", "automattic/jetpack-publicize": "@dev", "automattic/jetpack-search": "@dev", "automattic/jetpack-stats": "@dev", "automattic/jetpack-stats-admin": "@dev", "automattic/jetpack-sync": "@dev", "automattic/jetpack-videopress": "@dev", "automattic/jetpack-waf": "@dev", "automattic/jetpack-wordads": "@dev", "automattic/jetpack-yoast-promo": "@dev"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-14T20:12:40+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-config", "mirror-repo": "Automattic/jetpack-config", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-config/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/connection", "packages/import", "packages/jitm", "packages/post-list", "packages/publicize", "packages/search", "packages/stats", "packages/stats-admin", "packages/sync", "packages/videopress", "packages/waf", "packages/wordads", "packages/yoast-promo"]}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Jetpack configuration package that initializes other packages and configures Jetpack's functionality. Can be used as a base for all variants of Jetpack package usage.", "support": {"source": "https://github.com/Automattic/jetpack-config/tree/v3.0.0"}, "install-path": "../automattic/jetpack-config"}, {"name": "automattic/jetpack-connection", "version": "v6.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-connection.git", "reference": "52cd2ba7d845eb516d505959bd9a5e94d1bf4203"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-connection/zipball/52cd2ba7d845eb516d505959bd9a5e94d1bf4203", "reference": "52cd2ba7d845eb516d505959bd9a5e94d1bf4203", "shasum": ""}, "require": {"automattic/jetpack-a8c-mc-stats": "^3.0.0", "automattic/jetpack-admin-ui": "^0.5.1", "automattic/jetpack-assets": "^4.0.1", "automattic/jetpack-constants": "^3.0.1", "automattic/jetpack-redirect": "^3.0.1", "automattic/jetpack-roles": "^3.0.1", "automattic/jetpack-status": "^5.0.1", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "automattic/wordbless": "^0.4.2", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-12-09T15:47:56+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-connection", "mirror-repo": "Automattic/jetpack-connection", "branch-alias": {"dev-trunk": "6.2.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-connection/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/licensing", "packages/sync"]}, "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}}, "installation-source": "dist", "autoload": {"files": ["actions.php"], "classmap": ["legacy", "src/", "src/webhooks", "src/identity-crisis"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Everything needed to connect to the Jetpack infrastructure", "support": {"source": "https://github.com/Automattic/jetpack-connection/tree/v6.2.0"}, "install-path": "../automattic/jetpack-connection"}, {"name": "automattic/jetpack-constants", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-constants.git", "reference": "d4b7820defcdb40c1add88d5ebd722e4ba80a873"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-constants/zipball/d4b7820defcdb40c1add88d5ebd722e4ba80a873", "reference": "d4b7820defcdb40c1add88d5ebd722e4ba80a873", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-25T16:33:27+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-constants", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-constants/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A wrapper for defining constants in a more testable way.", "support": {"source": "https://github.com/Automattic/jetpack-constants/tree/v3.0.1"}, "install-path": "../automattic/jetpack-constants"}, {"name": "automattic/jetpack-redirect", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-redirect.git", "reference": "89732a3ba1c5eba8cfd948b7567823cd884102d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-redirect/zipball/89732a3ba1c5eba8cfd948b7567823cd884102d5", "reference": "89732a3ba1c5eba8cfd948b7567823cd884102d5", "shasum": ""}, "require": {"automattic/jetpack-status": "^5.0.1", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-25T16:34:01+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-redirect", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-redirect/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities to build URLs to the jetpack.com/redirect/ service", "support": {"source": "https://github.com/Automattic/jetpack-redirect/tree/v3.0.1"}, "install-path": "../automattic/jetpack-redirect"}, {"name": "automattic/jetpack-roles", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-roles.git", "reference": "fe5f2a45901ea14be00728119d097619615fb031"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-roles/zipball/fe5f2a45901ea14be00728119d097619615fb031", "reference": "fe5f2a45901ea14be00728119d097619615fb031", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-25T16:33:29+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-roles", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-roles/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities, related with user roles and capabilities.", "support": {"source": "https://github.com/Automattic/jetpack-roles/tree/v3.0.1"}, "install-path": "../automattic/jetpack-roles"}, {"name": "automattic/jetpack-status", "version": "v5.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-status.git", "reference": "769f55b6327187a85c14ed21943eea430f63220d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-status/zipball/769f55b6327187a85c14ed21943eea430f63220d", "reference": "769f55b6327187a85c14ed21943eea430f63220d", "shasum": ""}, "require": {"automattic/jetpack-constants": "^3.0.1", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^5.1.0", "automattic/jetpack-connection": "@dev", "automattic/jetpack-ip": "^0.4.1", "automattic/jetpack-plans": "@dev", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^1.1.1"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2024-11-25T16:33:53+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-status", "branch-alias": {"dev-trunk": "5.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-status/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/connection", "packages/plans"]}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Used to retrieve information about the current status of Jetpack and the site overall.", "support": {"source": "https://github.com/Automattic/jetpack-status/tree/v5.0.1"}, "install-path": "../automattic/jetpack-status"}, {"name": "composer/installers", "version": "v1.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/d20a64ed3c94748397ff5973488761b22f6d3f19", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "time": "2021-09-13T08:19:44+00:00", "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.12.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./installers"}, {"name": "maxmind-db/reader", "version": "v1.11.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "1e66f73ffcf25e17c7a910a1317e9720a95497c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/1e66f73ffcf25e17c7a910a1317e9720a95497c7", "reference": "1e66f73ffcf25e17c7a910a1317e9720a95497c7", "shasum": ""}, "require": {"php": ">=7.2"}, "conflict": {"ext-maxminddb": "<1.11.1,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "*", "phpunit/phpcov": ">=6.0.0", "phpunit/phpunit": ">=8.0.0,<10.0.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "time": "2023-12-02T00:09:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/MaxMind-DB-Reader-php/issues", "source": "https://github.com/maxmind/MaxMind-DB-Reader-php/tree/v1.11.1"}, "install-path": "../maxmind-db/reader"}, {"name": "opis/json-schema", "version": "2.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/json-schema.git", "reference": "712827751c62b465daae6e725bf0cf5ffbf965e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/json-schema/zipball/712827751c62b465daae6e725bf0cf5ffbf965e1", "reference": "712827751c62b465daae6e725bf0cf5ffbf965e1", "shasum": ""}, "require": {"ext-json": "*", "opis/string": "^2.0", "opis/uri": "^1.0", "php": "^7.4 || ^8.0"}, "require-dev": {"ext-bcmath": "*", "ext-intl": "*", "phpunit/phpunit": "^9.0"}, "time": "2024-12-30T20:20:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Opis\\JsonSchema\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Sorin Sarca", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON> Schema Validator for PHP", "homepage": "https://opis.io/json-schema", "keywords": ["json", "json-schema", "schema", "validation", "validator"], "support": {"issues": "https://github.com/opis/json-schema/issues", "source": "https://github.com/opis/json-schema/tree/2.4.1"}, "install-path": "../opis/json-schema"}, {"name": "opis/string", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/string.git", "reference": "9ebf1a1f873f502f6859d11210b25a4bf5d141e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/string/zipball/9ebf1a1f873f502f6859d11210b25a4bf5d141e7", "reference": "9ebf1a1f873f502f6859d11210b25a4bf5d141e7", "shasum": ""}, "require": {"ext-iconv": "*", "ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "time": "2022-01-14T15:42:23+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Opis\\String\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "Multibyte strings as objects", "homepage": "https://opis.io/string", "keywords": ["multi-byte", "opis", "string", "string manipulation", "utf-8"], "support": {"issues": "https://github.com/opis/string/issues", "source": "https://github.com/opis/string/tree/2.0.1"}, "install-path": "../opis/string"}, {"name": "opis/uri", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/uri.git", "reference": "0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/uri/zipball/0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a", "reference": "0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a", "shasum": ""}, "require": {"opis/string": "^2.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9"}, "time": "2021-05-22T15:57:08+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Opis\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "Build, parse and validate URIs and URI-templates", "homepage": "https://opis.io", "keywords": ["URI Template", "parse url", "punycode", "uri", "uri components", "url", "validate uri"], "support": {"issues": "https://github.com/opis/uri/issues", "source": "https://github.com/opis/uri/tree/1.1.0"}, "install-path": "../opis/uri"}, {"name": "pelago/emogrifier", "version": "v6.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MyIntervals/emogrifier.git", "reference": "aa72d5407efac118f3896bcb995a2cba793df0ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/emogrifier/zipball/aa72d5407efac118f3896bcb995a2cba793df0ae", "reference": "aa72d5407efac118f3896bcb995a2cba793df0ae", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0", "sabberworm/php-css-parser": "^8.3.1", "symfony/css-selector": "^3.4.32 || ^4.4 || ^5.3 || ^6.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.3.0", "phpunit/phpunit": "^8.5.16", "rawr/cross-data-providers": "^2.3.0"}, "time": "2021-09-16T16:22:04+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "7.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Pelago\\Emogrifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Converts CSS styles into inline style attributes in your HTML code", "homepage": "https://www.myintervals.com/emogrifier.php", "keywords": ["css", "email", "pre-processing"], "support": {"issues": "https://github.com/MyIntervals/emogrifier/issues", "source": "https://github.com/MyIntervals/emogrifier"}, "install-path": "../pelago/emogrifier"}, {"name": "sabberworm/php-css-parser", "version": "v8.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"phpunit/phpunit": "^5.7.27"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "time": "2024-07-01T07:33:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.6.0"}, "install-path": "../sabberworm/php-css-parser"}, {"name": "soundasleep/html2text", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/soundasleep/html2text.git", "reference": "83502b6f8f1aaef8e2e238897199d64f284b4af3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/soundasleep/html2text/zipball/83502b6f8f1aaef8e2e238897199d64f284b4af3", "reference": "83502b6f8f1aaef8e2e238897199d64f284b4af3", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.3|^8.0"}, "require-dev": {"phpstan/phpstan": "^1.9", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "time": "2023-01-06T09:28:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Soundasleep\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jevon.org", "role": "Developer"}], "description": "A PHP script to convert HTML into a plain text format", "homepage": "https://github.com/soundasleep/html2text", "keywords": ["email", "html", "php", "text"], "support": {"email": "<EMAIL>", "issues": "https://github.com/soundasleep/html2text/issues", "source": "https://github.com/soundasleep/html2text/tree/2.1.0"}, "install-path": "../soundasleep/html2text"}, {"name": "symfony/css-selector", "version": "v5.4.40", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "time": "2024-05-31T14:33:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/css-selector"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2024-05-31T15:07:36+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php80"}, {"name": "woocommerce/action-scheduler", "version": "3.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/woocommerce/action-scheduler.git", "reference": "efbb7953f72a433086335b249292f280dd43ddfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/action-scheduler/zipball/efbb7953f72a433086335b249292f280dd43ddfe", "reference": "efbb7953f72a433086335b249292f280dd43ddfe", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5", "woocommerce/woocommerce-sniffs": "0.1.0", "wp-cli/wp-cli": "~2.5.0", "yoast/phpunit-polyfills": "^2.0"}, "time": "2025-02-03T09:09:30+00:00", "type": "wordpress-plugin", "extra": {"scripts-description": {"test": "Run unit tests", "phpcs": "Analyze code against the WordPress coding standards with PHP_CodeSniffer", "phpcbf": "Fix coding standards warnings/errors automatically with PHP Code Beautifier"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "Action Scheduler for WordPress and WooCommerce", "homepage": "https://actionscheduler.org/", "support": {"issues": "https://github.com/woocommerce/action-scheduler/issues", "source": "https://github.com/woocommerce/action-scheduler/tree/3.9.2"}, "install-path": "../../packages/action-scheduler"}, {"name": "woocommerce/blueprint", "version": "0.0.1", "version_normalized": "*******", "dist": {"type": "path", "url": "../../packages/php/blueprint", "reference": "f996d43977196b9670d783b58c167243950876d3"}, "require": {"opis/json-schema": "^2.3"}, "require-dev": {"automattic/jetpack-changelogger": "3.3.0", "mockery/mockery": "^1.6", "phpunit/phpunit": "^9", "woocommerce/woocommerce-sniffs": "^1.0.0", "yoast/phpunit-polyfills": "^2.0"}, "type": "wordpress-plugin", "extra": {"changelogger": {"formatter": {"filename": "../../../tools/changelogger/class-legacy-core-formatter.php"}, "types": {"fix": "Fixes an existing bug", "add": "Adds functionality", "update": "Update existing functionality", "dev": "Development related task", "tweak": "A minor adjustment to the codebase", "performance": "Address performance issues", "enhancement": "Improve existing functionality"}, "changelog": "changelog.md"}}, "installation-source": "dist", "autoload": {"psr-4": {"Automattic\\WooCommerce\\Blueprint\\": "src/"}}, "autoload-dev": {"psr-4": {"Automattic\\WooCommerce\\Blueprint\\Tests\\": "tests/"}}, "scripts": {"test:setup": ["wp-env start"], "test:unit": ["wp-env run tests-cli --env-cwd=wp-content/plugins/blueprint ./vendor/bin/phpunit"], "phpcs": ["phpcs -s -p"], "phpcbf": ["phpcbf -p"]}, "transport-options": {"symlink": false, "relative": true}, "install-path": "../../packages/blueprint"}, {"name": "woocommerce/email-editor", "version": "dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core", "version_normalized": "dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core", "dist": {"type": "path", "url": "../../packages/php/email-editor", "reference": "956f59488f8336599f920f70267a6147149cb032"}, "require": {"php": ">=7.4", "soundasleep/html2text": "^2.1"}, "require-dev": {"automattic/jetpack-changelogger": "3.3.0"}, "type": "wordpress-plugin", "extra": {"changelogger": {"formatter": {"filename": "../../../tools/changelogger/class-package-formatter.php"}, "types": {"fix": "Fixes an existing bug", "add": "Adds functionality", "update": "Update existing functionality", "dev": "Development related task", "tweak": "A minor adjustment to the codebase", "performance": "Address performance issues", "enhancement": "Improve existing functionality"}, "changelog": "CHANGELOG.md"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/unit/"]}, "scripts": {"unit-test": ["../../../tests_env/vendor/bin/codecept run unit"], "integration-test": ["cd ../../../tests_env/docker && COMPOSE_HTTP_TIMEOUT=200 docker compose run -e SKIP_DEPS=1 -e SKIP_PLUGINS=1 -e PACKAGE_NAME=email-editor codeception_integration"], "code-style": ["../../../mailpoet/tasks/code_sniffer/vendor/bin/phpcs -ps"], "code-style-fix": ["../../../mailpoet/tasks/code_sniffer/vendor/bin/phpcbf -p"], "phpstan": ["php ./tasks/run-phpstan.php"]}, "license": ["GPL-3.0-or-later"], "description": "Email editor based on WordPress Gutenberg package.", "transport-options": {"symlink": false, "relative": true}, "install-path": "../../packages/email-editor"}], "dev": false, "dev-package-names": []}