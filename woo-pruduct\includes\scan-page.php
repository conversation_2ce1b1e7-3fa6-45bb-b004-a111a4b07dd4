<?php
/**
 * <PERSON><PERSON><PERSON>n tarama sayfası
 */

// <PERSON><PERSON>rudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

?>

<div class="wrap">
    <h1><?php _e('<PERSON><PERSON><PERSON><PERSON>', 'woo-pruduct'); ?></h1>
    
    <div class="woo-pruduct-scan-page">
        <!-- <PERSON><PERSON> Kontrolü -->
        <div class="scan-controls">
            <div class="scan-header">
                <h2><?php _e('Tutor LMS Kurslarına Bağlı Ürünleri Tara', 'woo-pruduct'); ?></h2>
                <p><?php _e('Bu araç, WooCommerce ürünlerini tarayarak Tutor LMS kurslarına bağlı olanları tespit eder ve sipariş durumlarını analiz eder.', 'woo-pruduct'); ?></p>
            </div>
            
            <div class="scan-actions">
                <button id="start-scan" class="button button-primary button-large">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e('Taramayı Başlat', 'woo-pruduct'); ?>
                </button>
                
                <button id="export-results" class="button button-secondary" style="display: none;">
                    <span class="dashicons dashicons-download"></span>
                    <?php _e('Sonuçları Dışa Aktar', 'woo-pruduct'); ?>
                </button>
            </div>
        </div>
        
        <!-- Tarama Durumu -->
        <div id="scan-progress" class="scan-progress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <p class="progress-text"><?php _e('Tarama başlatılıyor...', 'woo-pruduct'); ?></p>
        </div>
        
        <!-- Tarama Sonuçları -->
        <div id="scan-results" class="scan-results" style="display: none;">
            <h2><?php _e('Tarama Sonuçları', 'woo-pruduct'); ?></h2>
            <div class="results-summary">
                <div class="summary-item">
                    <span class="summary-label"><?php _e('Bulunan Ürün:', 'woo-pruduct'); ?></span>
                    <span class="summary-value" id="total-products">0</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label"><?php _e('Toplam Sipariş:', 'woo-pruduct'); ?></span>
                    <span class="summary-value" id="total-orders">0</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label"><?php _e('Tamamlanmış Sipariş:', 'woo-pruduct'); ?></span>
                    <span class="summary-value" id="completed-orders">0</span>
                </div>
            </div>
            
            <div class="results-table-container">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Kurs', 'woo-pruduct'); ?></th>
                            <th><?php _e('Ürün', 'woo-pruduct'); ?></th>
                            <th><?php _e('Fiyat', 'woo-pruduct'); ?></th>
                            <th><?php _e('Sipariş', 'woo-pruduct'); ?></th>
                            <th><?php _e('Tamamlanmış', 'woo-pruduct'); ?></th>
                            <th><?php _e('Durum', 'woo-pruduct'); ?></th>
                            <th><?php _e('İşlemler', 'woo-pruduct'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="results-tbody">
                        <!-- Sonuçlar buraya yüklenecek -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Detaylı Analiz -->
        <div id="detailed-analysis" class="detailed-analysis" style="display: none;">
            <h2><?php _e('Detaylı Analiz', 'woo-pruduct'); ?></h2>
            <div id="analysis-content">
                <!-- Analiz içeriği buraya yüklenecek -->
            </div>
        </div>
    </div>
</div>

<style>
.woo-pruduct-scan-page {
    max-width: 1200px;
}

.scan-controls {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.scan-header h2 {
    margin-top: 0;
    color: #333;
}

.scan-header p {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.scan-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.scan-actions .button {
    display: flex;
    align-items: center;
    gap: 8px;
}

.scan-progress {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    margin: 0;
    color: #666;
}

.scan-results {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
}

.summary-item {
    text-align: center;
}

.summary-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.summary-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

.results-table-container {
    overflow-x: auto;
}

.results-table-container table {
    min-width: 800px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active {
    background: #46b450;
    color: #fff;
}

.status-inactive {
    background: #dc3232;
    color: #fff;
}

.status-partial {
    background: #ffb900;
    color: #fff;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .button {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
}

.detailed-analysis {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
}

.analysis-chart {
    margin: 20px 0;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
    text-align: center;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading .button {
    position: relative;
}

.loading .button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.error-message {
    background: #ffeaea;
    border: 1px solid #dc3232;
    border-radius: 4px;
    padding: 15px;
    color: #dc3232;
    margin: 20px 0;
}

.success-message {
    background: #eafaea;
    border: 1px solid #46b450;
    border-radius: 4px;
    padding: 15px;
    color: #46b450;
    margin: 20px 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    let scanResults = [];
    
    $('#start-scan').on('click', function() {
        startScan();
    });
    
    $('#export-results').on('click', function() {
        exportResults();
    });
    
    function startScan() {
        const $button = $('#start-scan');
        const $progress = $('#scan-progress');
        const $results = $('#scan-results');
        
        // UI'yi güncelle
        $button.addClass('loading').prop('disabled', true);
        $progress.show();
        $results.hide();
        
        // Progress bar animasyonu
        animateProgress();
        
        // AJAX isteği
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'woo_pruduct_scan',
                nonce: wooPruductAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    scanResults = response.data;
                    displayResults(scanResults);
                    showSuccessMessage('<?php _e("Tarama başarıyla tamamlandı!", "woo-pruduct"); ?>');
                } else {
                    showErrorMessage(response.data || '<?php _e("Tarama sırasında bir hata oluştu.", "woo-pruduct"); ?>');
                }
            },
            error: function() {
                showErrorMessage('<?php _e("Sunucu hatası oluştu.", "woo-pruduct"); ?>');
            },
            complete: function() {
                $button.removeClass('loading').prop('disabled', false);
                $progress.hide();
            }
        });
    }
    
    function animateProgress() {
        const $fill = $('.progress-fill');
        const $text = $('.progress-text');
        
        let progress = 0;
        const interval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress > 90) {
                progress = 90;
            }
            
            $fill.css('width', progress + '%');
            
            if (progress < 30) {
                $text.text('<?php _e("Ürünler taranıyor...", "woo-pruduct"); ?>');
            } else if (progress < 60) {
                $text.text('<?php _e("Siparişler analiz ediliyor...", "woo-pruduct"); ?>');
            } else {
                $text.text('<?php _e("Sonuçlar hazırlanıyor...", "woo-pruduct"); ?>');
            }
        }, 200);
        
        // Tarama tamamlandığında interval'i temizle
        $(document).one('scanComplete', function() {
            clearInterval(interval);
            $fill.css('width', '100%');
            $text.text('<?php _e("Tarama tamamlandı!", "woo-pruduct"); ?>');
        });
    }
    
    function displayResults(results) {
        const $results = $('#scan-results');
        const $tbody = $('#results-tbody');
        
        // Özet istatistikleri güncelle
        $('#total-products').text(results.length);
        
        let totalOrders = 0;
        let completedOrders = 0;
        
        results.forEach(function(item) {
            totalOrders += item.orders_count;
            completedOrders += item.completed_orders;
        });
        
        $('#total-orders').text(totalOrders);
        $('#completed-orders').text(completedOrders);
        
        // Tablo içeriğini temizle
        $tbody.empty();
        
        // Sonuçları tabloya ekle
        results.forEach(function(item) {
            const completionRate = item.orders_count > 0 ? 
                Math.round((item.completed_orders / item.orders_count) * 100) : 0;
            
            let statusClass = 'status-inactive';
            let statusText = '<?php _e("Pasif", "woo-pruduct"); ?>';
            
            if (completionRate > 70) {
                statusClass = 'status-active';
                statusText = '<?php _e("Aktif", "woo-pruduct"); ?>';
            } else if (completionRate > 30) {
                statusClass = 'status-partial';
                statusText = '<?php _e("Kısmi", "woo-pruduct"); ?>';
            }
            
            const row = `
                <tr>
                    <td>
                        <strong>${item.course_title}</strong><br>
                        <small>ID: ${item.course_id}</small>
                    </td>
                    <td>
                        <strong>${item.product_title}</strong><br>
                        <small>ID: ${item.product_id}</small>
                    </td>
                    <td>${item.product_price ? item.product_price + ' ₺' : '-'}</td>
                    <td>${item.orders_count}</td>
                    <td>${item.completed_orders} (${completionRate}%)</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="button button-small view-details" data-product-id="${item.product_id}">
                                <?php _e("Detay", "woo-pruduct"); ?>
                            </button>
                            <button class="button button-small toggle-status" data-product-id="${item.product_id}">
                                <?php _e("Değiştir", "woo-pruduct"); ?>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            
            $tbody.append(row);
        });
        
        // Sonuçları göster
        $results.show();
        $('#export-results').show();
        
        // Tarama tamamlandı eventi
        $(document).trigger('scanComplete');
    }
    
    function exportResults() {
        if (scanResults.length === 0) {
            showErrorMessage('<?php _e("Dışa aktarılacak veri bulunamadı.", "woo-pruduct"); ?>');
            return;
        }
        
        // CSV formatında dışa aktar
        let csv = 'Kurs ID,Kurs Adı,Ürün ID,Ürün Adı,Fiyat,Sipariş Sayısı,Tamamlanmış Sipariş\n';
        
        scanResults.forEach(function(item) {
            csv += `${item.course_id},"${item.course_title}",${item.product_id},"${item.product_title}",${item.product_price},${item.orders_count},${item.completed_orders}\n`;
        });
        
        // Dosyayı indir
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'woo-pruduct-scan-results.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    function showSuccessMessage(message) {
        const $message = $('<div class="success-message">' + message + '</div>');
        $('.woo-pruduct-scan-page').prepend($message);
        setTimeout(function() {
            $message.fadeOut(function() {
                $message.remove();
            });
        }, 5000);
    }
    
    function showErrorMessage(message) {
        const $message = $('<div class="error-message">' + message + '</div>');
        $('.woo-pruduct-scan-page').prepend($message);
        setTimeout(function() {
            $message.fadeOut(function() {
                $message.remove();
            });
        }, 5000);
    }
    
    // Detay görüntüleme
    $(document).on('click', '.view-details', function() {
        const productId = $(this).data('product-id');
        const product = scanResults.find(item => item.product_id == productId);
        
        if (product) {
            showProductDetails(product);
        }
    });
    
    // Durum değiştirme
    $(document).on('click', '.toggle-status', function() {
        const productId = $(this).data('product-id');
        toggleProductStatus(productId);
    });
    
    function showProductDetails(product) {
        let detailsHtml = `
            <h3>${product.course_title}</h3>
            <p><strong><?php _e("Ürün:", "woo-pruduct"); ?></strong> ${product.product_title}</p>
            <p><strong><?php _e("Fiyat:", "woo-pruduct"); ?></strong> ${product.product_price} ₺</p>
            <p><strong><?php _e("Toplam Sipariş:", "woo-pruduct"); ?></strong> ${product.orders_count}</p>
            <p><strong><?php _e("Tamamlanmış Sipariş:", "woo-pruduct"); ?></strong> ${product.completed_orders}</p>
        `;
        
        if (product.orders && product.orders.length > 0) {
            detailsHtml += '<h4><?php _e("Son Siparişler:", "woo-pruduct"); ?></h4><ul>';
            product.orders.slice(0, 5).forEach(function(order) {
                detailsHtml += `<li><?php _e("Sipariş", "woo-pruduct"); ?> #${order.order_id} - ${order.order_status} (${order.post_date})</li>`;
            });
            detailsHtml += '</ul>';
        }
        
        $('#analysis-content').html(detailsHtml);
        $('#detailed-analysis').show();
    }
    
    function toggleProductStatus(productId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'woo_pruduct_toggle_button',
                product_id: productId,
                action_type: 'toggle',
                nonce: wooPruductAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('<?php _e("Durum başarıyla değiştirildi.", "woo-pruduct"); ?>');
                } else {
                    showErrorMessage(response.data || '<?php _e("Durum değiştirilemedi.", "woo-pruduct"); ?>');
                }
            },
            error: function() {
                showErrorMessage('<?php _e("Sunucu hatası oluştu.", "woo-pruduct"); ?>');
            }
        });
    }
});
</script>
