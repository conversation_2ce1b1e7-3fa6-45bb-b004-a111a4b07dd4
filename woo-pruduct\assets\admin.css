/**
 * Woo Product Manager Admin CSS
 */

/* <PERSON><PERSON> */
.woo-pruduct-dashboard,
.woo-pruduct-scan-page,
.woo-pruduct-logs-page {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Dashboard Kartları */
.woo-pruduct-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-icon {
    margin-right: 15px;
    flex-shrink: 0;
}

.stat-icon .dashicons {
    font-size: 40px;
    color: #0073aa;
    width: 40px;
    height: 40px;
}

.stat-content h3 {
    margin: 0;
    font-size: 32px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
}

/* Hızlı İşlemler */
.woo-pruduct-quick-actions,
.woo-pruduct-recent-activity,
.woo-pruduct-system-status {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border: 2px solid #0073aa;
    border-radius: 6px;
    text-decoration: none;
    color: #0073aa;
    background: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    min-height: 50px;
}

.quick-action-btn:hover,
.quick-action-btn:focus {
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    outline: none;
}

.quick-action-btn .dashicons {
    margin-right: 8px;
    font-size: 16px;
}

/* Aktivite Listesi */
.activity-list {
    margin-top: 15px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    margin-right: 15px;
    margin-top: 2px;
    flex-shrink: 0;
}

.activity-icon .dashicons {
    font-size: 20px;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 5px 0;
    line-height: 1.4;
}

.activity-meta {
    color: #666;
    font-size: 12px;
}

/* Durum Rozetleri */
.status-badge {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    display: inline-block;
}

.status-completed {
    background: #46b450;
    color: #fff;
}

.status-pending {
    background: #ffb900;
    color: #fff;
}

.status-processing {
    background: #00a0d2;
    color: #fff;
}

.status-cancelled {
    background: #dc3232;
    color: #fff;
}

.status-refunded {
    background: #ff6900;
    color: #fff;
}

/* Sistem Durumu */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.status-label {
    font-weight: 500;
}

.status-value {
    font-weight: bold;
}

.status-ok {
    color: #46b450;
}

.status-warning {
    color: #ffb900;
}

.status-error {
    color: #dc3232;
}

/* Tarama Sayfası */
.scan-controls {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.scan-header h2 {
    margin-top: 0;
    color: #333;
}

.scan-header p {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
}

.scan-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.scan-actions .button {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Progress Bar */
.scan-progress {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Sonuçlar */
.scan-results {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
}

.summary-item {
    text-align: center;
}

.summary-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.summary-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

.results-table-container {
    overflow-x: auto;
}

.results-table-container table {
    min-width: 800px;
}

/* İşlem Butonları */
.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.action-buttons .button {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
    min-height: auto;
}

/* Loading Durumu */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading .button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Mesajlar */
.error-message {
    background: #ffeaea;
    border: 1px solid #dc3232;
    border-radius: 4px;
    padding: 15px;
    color: #dc3232;
    margin: 20px 0;
}

.success-message {
    background: #eafaea;
    border: 1px solid #46b450;
    border-radius: 4px;
    padding: 15px;
    color: #46b450;
    margin: 20px 0;
}

.warning-message {
    background: #fff8e1;
    border: 1px solid #ffb900;
    border-radius: 4px;
    padding: 15px;
    color: #f57c00;
    margin: 20px 0;
}

/* Log Sayfası */
.log-filters {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-controls select {
    min-width: 150px;
}

.log-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.log-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.log-type-order {
    background: #e1f5fe;
    color: #0277bd;
}

.log-type-button {
    background: #f3e5f5;
    color: #7b1fa2;
}

.log-details {
    font-size: 12px;
    line-height: 1.4;
}

.status-change {
    font-family: monospace;
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
}

/* Boş Durum */
.no-activity,
.no-logs {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-activity .dashicons,
.no-logs .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.no-activity h3,
.no-logs h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
}

.no-activity p,
.no-logs p {
    margin: 0;
    font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
    .woo-pruduct-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .log-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        justify-content: center;
    }
    
    .log-stats {
        justify-content: space-around;
    }
    
    .results-table-container,
    .log-table-container {
        overflow-x: auto;
    }
    
    .scan-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .scan-actions .button {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stat-content h3 {
        font-size: 24px;
    }
    
    .summary-value {
        font-size: 20px;
    }
    
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .activity-icon {
        margin-right: 0;
        margin-bottom: 5px;
    }
}
