/*! For license information please see jetpack-connection.js.LICENSE.txt */
!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.JetpackConnection=n():e.JetpackConnection=n()}(self,(()=>(()=>{var e={7689:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={error:"TcCZnGE6mad8Dvz9pCZi",button:"_mn6o2Dtm5pfFWc8_A1K"}},2258:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},9535:()=>{},8403:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},6406:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={placeholder:"NisihrgiIKl_knpYJtfg",pulse:"R2i0K45dEF157drbVRPI"}},4319:()=>{},8325:()=>{},9634:()=>{},4495:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},1772:()=>{},9064:()=>{},381:()=>{},4175:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={heading:"urouayitSUT8zW0V3p_0",notice:"iXXJlk08gFDeCvsTTlNQ",button:"MWqRqr7q6fgvLxitcWYk","bigger-than-medium":"YLcXAoc82nypTPaKSAcd",error:"e6hHy8BZ7ZKPSXbIC0UG",message:"jXz8LnXNzMDdtHqkG0sZ"}},7419:()=>{},785:()=>{},255:()=>{},3732:()=>{},2057:()=>{},9903:(e,n,t)=>{"use strict";t.d(n,{i:()=>i});const o={AED:{symbol:"د.إ.‏",grouping:",",decimal:".",precision:2},AFN:{symbol:"؋",grouping:",",decimal:".",precision:2},ALL:{symbol:"Lek",grouping:".",decimal:",",precision:2},AMD:{symbol:"֏",grouping:",",decimal:".",precision:2},ANG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AOA:{symbol:"Kz",grouping:",",decimal:".",precision:2},ARS:{symbol:"$",grouping:".",decimal:",",precision:2},AUD:{symbol:"A$",grouping:",",decimal:".",precision:2},AWG:{symbol:"ƒ",grouping:",",decimal:".",precision:2},AZN:{symbol:"₼",grouping:" ",decimal:",",precision:2},BAM:{symbol:"КМ",grouping:".",decimal:",",precision:2},BBD:{symbol:"Bds$",grouping:",",decimal:".",precision:2},BDT:{symbol:"৳",grouping:",",decimal:".",precision:0},BGN:{symbol:"лв.",grouping:" ",decimal:",",precision:2},BHD:{symbol:"د.ب.‏",grouping:",",decimal:".",precision:3},BIF:{symbol:"FBu",grouping:",",decimal:".",precision:0},BMD:{symbol:"$",grouping:",",decimal:".",precision:2},BND:{symbol:"$",grouping:".",decimal:",",precision:0},BOB:{symbol:"Bs",grouping:".",decimal:",",precision:2},BRL:{symbol:"R$",grouping:".",decimal:",",precision:2},BSD:{symbol:"$",grouping:",",decimal:".",precision:2},BTC:{symbol:"Ƀ",grouping:",",decimal:".",precision:2},BTN:{symbol:"Nu.",grouping:",",decimal:".",precision:1},BWP:{symbol:"P",grouping:",",decimal:".",precision:2},BYR:{symbol:"р.",grouping:" ",decimal:",",precision:2},BZD:{symbol:"BZ$",grouping:",",decimal:".",precision:2},CAD:{symbol:"C$",grouping:",",decimal:".",precision:2},CDF:{symbol:"FC",grouping:",",decimal:".",precision:2},CHF:{symbol:"CHF",grouping:"'",decimal:".",precision:2},CLP:{symbol:"$",grouping:".",decimal:",",precision:2},CNY:{symbol:"¥",grouping:",",decimal:".",precision:2},COP:{symbol:"$",grouping:".",decimal:",",precision:2},CRC:{symbol:"₡",grouping:".",decimal:",",precision:2},CUC:{symbol:"CUC",grouping:",",decimal:".",precision:2},CUP:{symbol:"$MN",grouping:",",decimal:".",precision:2},CVE:{symbol:"$",grouping:",",decimal:".",precision:2},CZK:{symbol:"Kč",grouping:" ",decimal:",",precision:2},DJF:{symbol:"Fdj",grouping:",",decimal:".",precision:0},DKK:{symbol:"kr.",grouping:"",decimal:",",precision:2},DOP:{symbol:"RD$",grouping:",",decimal:".",precision:2},DZD:{symbol:"د.ج.‏",grouping:",",decimal:".",precision:2},EGP:{symbol:"ج.م.‏",grouping:",",decimal:".",precision:2},ERN:{symbol:"Nfk",grouping:",",decimal:".",precision:2},ETB:{symbol:"ETB",grouping:",",decimal:".",precision:2},EUR:{symbol:"€",grouping:".",decimal:",",precision:2},FJD:{symbol:"FJ$",grouping:",",decimal:".",precision:2},FKP:{symbol:"£",grouping:",",decimal:".",precision:2},GBP:{symbol:"£",grouping:",",decimal:".",precision:2},GEL:{symbol:"Lari",grouping:" ",decimal:",",precision:2},GHS:{symbol:"₵",grouping:",",decimal:".",precision:2},GIP:{symbol:"£",grouping:",",decimal:".",precision:2},GMD:{symbol:"D",grouping:",",decimal:".",precision:2},GNF:{symbol:"FG",grouping:",",decimal:".",precision:0},GTQ:{symbol:"Q",grouping:",",decimal:".",precision:2},GYD:{symbol:"G$",grouping:",",decimal:".",precision:2},HKD:{symbol:"HK$",grouping:",",decimal:".",precision:2},HNL:{symbol:"L.",grouping:",",decimal:".",precision:2},HRK:{symbol:"kn",grouping:".",decimal:",",precision:2},HTG:{symbol:"G",grouping:",",decimal:".",precision:2},HUF:{symbol:"Ft",grouping:".",decimal:",",precision:0},IDR:{symbol:"Rp",grouping:".",decimal:",",precision:0},ILS:{symbol:"₪",grouping:",",decimal:".",precision:2},INR:{symbol:"₹",grouping:",",decimal:".",precision:2},IQD:{symbol:"د.ع.‏",grouping:",",decimal:".",precision:2},IRR:{symbol:"﷼",grouping:",",decimal:"/",precision:2},ISK:{symbol:"kr.",grouping:".",decimal:",",precision:0},JMD:{symbol:"J$",grouping:",",decimal:".",precision:2},JOD:{symbol:"د.ا.‏",grouping:",",decimal:".",precision:3},JPY:{symbol:"¥",grouping:",",decimal:".",precision:0},KES:{symbol:"S",grouping:",",decimal:".",precision:2},KGS:{symbol:"сом",grouping:" ",decimal:"-",precision:2},KHR:{symbol:"៛",grouping:",",decimal:".",precision:0},KMF:{symbol:"CF",grouping:",",decimal:".",precision:2},KPW:{symbol:"₩",grouping:",",decimal:".",precision:0},KRW:{symbol:"₩",grouping:",",decimal:".",precision:0},KWD:{symbol:"د.ك.‏",grouping:",",decimal:".",precision:3},KYD:{symbol:"$",grouping:",",decimal:".",precision:2},KZT:{symbol:"₸",grouping:" ",decimal:"-",precision:2},LAK:{symbol:"₭",grouping:",",decimal:".",precision:0},LBP:{symbol:"ل.ل.‏",grouping:",",decimal:".",precision:2},LKR:{symbol:"₨",grouping:",",decimal:".",precision:0},LRD:{symbol:"L$",grouping:",",decimal:".",precision:2},LSL:{symbol:"M",grouping:",",decimal:".",precision:2},LYD:{symbol:"د.ل.‏",grouping:",",decimal:".",precision:3},MAD:{symbol:"د.م.‏",grouping:",",decimal:".",precision:2},MDL:{symbol:"lei",grouping:",",decimal:".",precision:2},MGA:{symbol:"Ar",grouping:",",decimal:".",precision:0},MKD:{symbol:"ден.",grouping:".",decimal:",",precision:2},MMK:{symbol:"K",grouping:",",decimal:".",precision:2},MNT:{symbol:"₮",grouping:" ",decimal:",",precision:2},MOP:{symbol:"MOP$",grouping:",",decimal:".",precision:2},MRO:{symbol:"UM",grouping:",",decimal:".",precision:2},MTL:{symbol:"₤",grouping:",",decimal:".",precision:2},MUR:{symbol:"₨",grouping:",",decimal:".",precision:2},MVR:{symbol:"MVR",grouping:",",decimal:".",precision:1},MWK:{symbol:"MK",grouping:",",decimal:".",precision:2},MXN:{symbol:"MX$",grouping:",",decimal:".",precision:2},MYR:{symbol:"RM",grouping:",",decimal:".",precision:2},MZN:{symbol:"MT",grouping:",",decimal:".",precision:0},NAD:{symbol:"N$",grouping:",",decimal:".",precision:2},NGN:{symbol:"₦",grouping:",",decimal:".",precision:2},NIO:{symbol:"C$",grouping:",",decimal:".",precision:2},NOK:{symbol:"kr",grouping:" ",decimal:",",precision:2},NPR:{symbol:"₨",grouping:",",decimal:".",precision:2},NZD:{symbol:"NZ$",grouping:",",decimal:".",precision:2},OMR:{symbol:"﷼",grouping:",",decimal:".",precision:3},PAB:{symbol:"B/.",grouping:",",decimal:".",precision:2},PEN:{symbol:"S/.",grouping:",",decimal:".",precision:2},PGK:{symbol:"K",grouping:",",decimal:".",precision:2},PHP:{symbol:"₱",grouping:",",decimal:".",precision:2},PKR:{symbol:"₨",grouping:",",decimal:".",precision:2},PLN:{symbol:"zł",grouping:" ",decimal:",",precision:2},PYG:{symbol:"₲",grouping:".",decimal:",",precision:2},QAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},RON:{symbol:"lei",grouping:".",decimal:",",precision:2},RSD:{symbol:"Дин.",grouping:".",decimal:",",precision:2},RUB:{symbol:"₽",grouping:" ",decimal:",",precision:2},RWF:{symbol:"RWF",grouping:" ",decimal:",",precision:2},SAR:{symbol:"﷼",grouping:",",decimal:".",precision:2},SBD:{symbol:"S$",grouping:",",decimal:".",precision:2},SCR:{symbol:"₨",grouping:",",decimal:".",precision:2},SDD:{symbol:"LSd",grouping:",",decimal:".",precision:2},SDG:{symbol:"£‏",grouping:",",decimal:".",precision:2},SEK:{symbol:"kr",grouping:",",decimal:".",precision:2},SGD:{symbol:"S$",grouping:",",decimal:".",precision:2},SHP:{symbol:"£",grouping:",",decimal:".",precision:2},SLL:{symbol:"Le",grouping:",",decimal:".",precision:2},SOS:{symbol:"S",grouping:",",decimal:".",precision:2},SRD:{symbol:"$",grouping:",",decimal:".",precision:2},STD:{symbol:"Db",grouping:",",decimal:".",precision:2},SVC:{symbol:"₡",grouping:",",decimal:".",precision:2},SYP:{symbol:"£",grouping:",",decimal:".",precision:2},SZL:{symbol:"E",grouping:",",decimal:".",precision:2},THB:{symbol:"฿",grouping:",",decimal:".",precision:2},TJS:{symbol:"TJS",grouping:" ",decimal:";",precision:2},TMT:{symbol:"m",grouping:" ",decimal:",",precision:0},TND:{symbol:"د.ت.‏",grouping:",",decimal:".",precision:3},TOP:{symbol:"T$",grouping:",",decimal:".",precision:2},TRY:{symbol:"TL",grouping:".",decimal:",",precision:2},TTD:{symbol:"TT$",grouping:",",decimal:".",precision:2},TVD:{symbol:"$T",grouping:",",decimal:".",precision:2},TWD:{symbol:"NT$",grouping:",",decimal:".",precision:2},TZS:{symbol:"TSh",grouping:",",decimal:".",precision:2},UAH:{symbol:"₴",grouping:" ",decimal:",",precision:2},UGX:{symbol:"USh",grouping:",",decimal:".",precision:2},USD:{symbol:"$",grouping:",",decimal:".",precision:2},UYU:{symbol:"$U",grouping:".",decimal:",",precision:2},UZS:{symbol:"сўм",grouping:" ",decimal:",",precision:2},VEB:{symbol:"Bs.",grouping:",",decimal:".",precision:2},VEF:{symbol:"Bs. F.",grouping:".",decimal:",",precision:2},VND:{symbol:"₫",grouping:".",decimal:",",precision:1},VUV:{symbol:"VT",grouping:",",decimal:".",precision:0},WST:{symbol:"WS$",grouping:",",decimal:".",precision:2},XAF:{symbol:"F",grouping:",",decimal:".",precision:2},XCD:{symbol:"$",grouping:",",decimal:".",precision:2},XOF:{symbol:"F",grouping:" ",decimal:",",precision:2},XPF:{symbol:"F",grouping:",",decimal:".",precision:2},YER:{symbol:"﷼",grouping:",",decimal:".",precision:2},ZAR:{symbol:"R",grouping:" ",decimal:",",precision:2},ZMW:{symbol:"ZK",grouping:",",decimal:".",precision:2},WON:{symbol:"₩",grouping:",",decimal:".",precision:2}};function i(e){return o[e]||{symbol:"$",grouping:",",decimal:".",precision:2}}},4761:(e,n,t)=>{"use strict";t.d(n,{vA:()=>c});var o=t(9903),i=t(8449);function c(e,n,t={}){const c=(0,o.i)(n);if(!c||isNaN(e))return null;const{decimal:r,grouping:s,precision:a,symbol:l}={...c,...t},p=e<0?"-":"",d=Math.abs(e),u=Math.floor(d);return{sign:p,symbol:l,integer:(0,i.A)(d,a,r,s).split(r)[0],fraction:a>0?(0,i.A)(d-u,a,r,s).slice(1):""}}},8449:(e,n,t)=>{"use strict";function o(e,n=0,t=".",o=","){const i=(e+"").replace(/[^0-9+\-Ee.]/g,""),c=isFinite(+i)?+i:0,r=isFinite(+n)?Math.abs(n):0,s=(r?function(e,n){const t=Math.pow(10,n);return""+(Math.round(e*t)/t).toFixed(n)}(c,r):""+Math.round(c)).split(".");return s[0].length>3&&(s[0]=s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,o)),(s[1]||"").length<r&&(s[1]=s[1]||"",s[1]+=new Array(r-s[1].length+1).join("0")),s.join(t)}t.d(n,{A:()=>o})},7750:(e,n,t)=>{"use strict";t.d(n,{A:()=>i});var o=t(6087);const i=(0,o.forwardRef)((function({icon:e,size:n=24,...t},i){return(0,o.cloneElement)(e,{width:n,height:n,...t,ref:i})}))},1386:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(5573),i=t(790);const c=(0,i.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(o.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},8391:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(5573),i=t(790);const c=(0,i.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(o.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},4804:(e,n,t)=>{n.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;n.splice(1,0,t,"color: inherit");let o=0,i=0;n[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(i=o))})),n.splice(i,0,t)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},n.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),e.exports=t(5067)(n);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},5067:(e,n,t)=>{e.exports=function(e){function n(e){let t,i,c,r=null;function s(...e){if(!s.enabled)return;const o=s,i=Number(new Date),c=i-(t||i);o.diff=c,o.prev=t,o.curr=i,t=i,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let r=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,i)=>{if("%%"===t)return"%";r++;const c=n.formatters[i];if("function"==typeof c){const n=e[r];t=c.call(o,n),e.splice(r,1),r--}return t})),n.formatArgs.call(o,e);(o.log||n.log).apply(o,e)}return s.namespace=e,s.useColors=n.useColors(),s.color=n.selectColor(e),s.extend=o,s.destroy=n.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(i!==n.namespaces&&(i=n.namespaces,c=n.enabled(e)),c),set:e=>{r=e}}),"function"==typeof n.init&&n.init(s),s}function o(e,t){const o=n(this.namespace+(void 0===t?":":t)+e);return o.log=this.log,o}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){const e=[...n.names.map(i),...n.skips.map(i).map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){let t;n.save(e),n.namespaces=e,n.names=[],n.skips=[];const o=("string"==typeof e?e:"").split(/[\s,]+/),i=o.length;for(t=0;t<i;t++)o[t]&&("-"===(e=o[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.slice(1)+"$")):n.names.push(new RegExp("^"+e+"$")))},n.enabled=function(e){if("*"===e[e.length-1])return!0;let t,o;for(t=0,o=n.skips.length;t<o;t++)if(n.skips[t].test(e))return!1;for(t=0,o=n.names.length;t<o;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=t(3594),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((t=>{n[t]=e[t]})),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n}},3594:e=>{var n=1e3,t=60*n,o=60*t,i=24*o,c=7*i,r=365.25*i;function s(e,n,t,o){var i=n>=1.5*t;return Math.round(e/t)+" "+o+(i?"s":"")}e.exports=function(e,a){a=a||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!s)return;var a=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return a*r;case"weeks":case"week":case"w":return a*c;case"days":case"day":case"d":return a*i;case"hours":case"hour":case"hrs":case"hr":case"h":return a*o;case"minutes":case"minute":case"mins":case"min":case"m":return a*t;case"seconds":case"second":case"secs":case"sec":case"s":return a*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}(e);if("number"===l&&isFinite(e))return a.long?function(e){var c=Math.abs(e);if(c>=i)return s(e,c,i,"day");if(c>=o)return s(e,c,o,"hour");if(c>=t)return s(e,c,t,"minute");if(c>=n)return s(e,c,n,"second");return e+" ms"}(e):function(e){var c=Math.abs(e);if(c>=i)return Math.round(e/i)+"d";if(c>=o)return Math.round(e/o)+"h";if(c>=t)return Math.round(e/t)+"m";if(c>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1583:(e,n,t)=>{"use strict";var o=t(1752);function i(){}function c(){}c.resetWarningCache=i,e.exports=function(){function e(e,n,t,i,c,r){if(r!==o){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function n(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:n,element:e,elementType:e,instanceOf:n,node:e,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:c,resetWarningCache:i};return t.PropTypes=t,t}},3619:(e,n,t)=>{e.exports=t(1583)()},1752:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(4804);const i=t.n(o)()("dops:analytics");let c,r;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const s={initialize:function(e,n,t){s.setUser(e,n),s.setSuperProps(t),s.identifyUser()},setGoogleAnalyticsEnabled:function(e,n=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=n},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,n){r={ID:e,username:n}},setSuperProps:function(e){c=e},assignSuperProps:function(e){c=Object.assign(c||{},e)},mc:{bumpStat:function(e,n){const t=function(e,n){let t="";if("object"==typeof e){for(const n in e)t+="&x_"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);i("Bumping stats %o",e)}else t="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(n),i('Bumping stat "%s" in group "%s"',n,e);return t}(e,n);s.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+t+"&t="+Math.random())},bumpStatWithPageView:function(e,n){const t=function(e,n){let t="";if("object"==typeof e){for(const n in e)t+="&"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);i("Built stats %o",e)}else t="&"+encodeURIComponent(e)+"="+encodeURIComponent(n),i('Built stat "%s" in group "%s"',n,e);return t}(e,n);s.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+t+"&t="+Math.random())}},pageView:{record:function(e,n){s.tracks.recordPageView(e),s.ga.recordPageView(e,n)}},purchase:{record:function(e,n,t,o,i,c,r){s.ga.recordPurchase(e,n,t,o,i,c,r)}},tracks:{recordEvent:function(e,n){n=n||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(c&&(i("- Super Props: %o",c),n=Object.assign(n,c)),i('Record event "%s" called with props %s',e,JSON.stringify(n)),window._tkq.push(["recordEvent",e,n])):i('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const n="object"==typeof e?e:{target:e};s.tracks.recordEvent("jetpack_wpa_click",n)},recordPageView:function(e){s.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){i("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};s.ga.initialized||(r&&(e={userId:"u-"+r.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),s.ga.initialized=!0)},recordPageView:function(e,n){s.ga.initialize(),i("Recording Page View ~ [URL: "+e+"] [Title: "+n+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:n}))},recordEvent:function(e,n,t,o){s.ga.initialize();let c="Recording Event ~ [Category: "+e+"] [Action: "+n+"]";void 0!==t&&(c+=" [Option Label: "+t+"]"),void 0!==o&&(c+=" [Option Value: "+o+"]"),i(c),this.googleAnalyticsEnabled&&window.ga("send","event",e,n,t,o)},recordPurchase:function(e,n,t,o,i,c,r){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:r}),window.ga("ecommerce:addItem",{id:e,name:n,sku:t,price:i,quantity:c}),window.ga("ecommerce:send")}},identifyUser:function(){r&&window._tkq.push(["identifyUser",r.ID,r.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},a=s},5932:(e,n,t)=>{"use strict";t.d(n,{Ay:()=>d});var o=t(6439),i=t(3832);function c(e){class n extends Error{constructor(...n){super(...n),this.name=e}}return n}const r=c("JsonParseError"),s=c("JsonParseAfterRedirectError"),a=c("Api404Error"),l=c("Api404AfterRedirectError"),p=c("FetchNetworkError");const d=new function(e,n){let t=e,c=e,r={"X-WP-Nonce":n},s={credentials:"same-origin",headers:r},a={method:"post",credentials:"same-origin",headers:Object.assign({},r,{"Content-type":"application/json"})},l=function(e){const n=e.split("?"),t=n.length>1?n[1]:"",o=t.length?t.split("&"):[];return o.push("_cacheBuster="+(new Date).getTime()),n[0]+"?"+o.join("&")};const p={setApiRoot(e){t=e},setWpcomOriginApiUrl(e){c=e},setApiNonce(e){r={"X-WP-Nonce":e},s={credentials:"same-origin",headers:r},a={method:"post",credentials:"same-origin",headers:Object.assign({},r,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,n,i)=>{const c={registration_nonce:e,no_iframe:!0};return(0,o.jetpackConfigHas)("consumer_slug")&&(c.plugin_slug=(0,o.jetpackConfigGet)("consumer_slug")),null!==n&&(c.redirect_uri=n),i&&(c.from=i),g(`${t}jetpack/v4/connection/register`,a,{body:JSON.stringify(c)}).then(u).then(m)},fetchAuthorizationUrl:e=>d((0,i.addQueryArgs)(`${t}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),s).then(u).then(m),fetchSiteConnectionData:()=>d(`${t}jetpack/v4/connection/data`,s).then(m),fetchSiteConnectionStatus:()=>d(`${t}jetpack/v4/connection`,s).then(m),fetchSiteConnectionTest:()=>d(`${t}jetpack/v4/connection/test`,s).then(u).then(m),fetchUserConnectionData:()=>d(`${t}jetpack/v4/connection/data`,s).then(m),fetchUserTrackingSettings:()=>d(`${t}jetpack/v4/tracking/settings`,s).then(u).then(m),updateUserTrackingSettings:e=>g(`${t}jetpack/v4/tracking/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),disconnectSite:()=>g(`${t}jetpack/v4/connection`,a,{body:JSON.stringify({isActive:!1})}).then(u).then(m),fetchConnectUrl:()=>d(`${t}jetpack/v4/connection/url`,s).then(u).then(m),unlinkUser:()=>g(`${t}jetpack/v4/connection/user`,a,{body:JSON.stringify({linked:!1})}).then(u).then(m),reconnect:()=>g(`${t}jetpack/v4/connection/reconnect`,a).then(u).then(m),fetchConnectedPlugins:()=>d(`${t}jetpack/v4/connection/plugins`,s).then(u).then(m),setHasSeenWCConnectionModal:()=>g(`${t}jetpack/v4/seen-wc-connection-modal`,a).then(u).then(m),fetchModules:()=>d(`${t}jetpack/v4/module/all`,s).then(u).then(m),fetchModule:e=>d(`${t}jetpack/v4/module/${e}`,s).then(u).then(m),activateModule:e=>g(`${t}jetpack/v4/module/${e}/active`,a,{body:JSON.stringify({active:!0})}).then(u).then(m),deactivateModule:e=>g(`${t}jetpack/v4/module/${e}/active`,a,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,n)=>g(`${t}jetpack/v4/module/${e}`,a,{body:JSON.stringify(n)}).then(u).then(m),updateSettings:e=>g(`${t}jetpack/v4/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),getProtectCount:()=>d(`${t}jetpack/v4/module/protect/data`,s).then(u).then(m),resetOptions:e=>g(`${t}jetpack/v4/options/${e}`,a,{body:JSON.stringify({reset:!0})}).then(u).then(m),activateVaultPress:()=>g(`${t}jetpack/v4/plugins`,a,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(u).then(m),getVaultPressData:()=>d(`${t}jetpack/v4/module/vaultpress/data`,s).then(u).then(m),installPlugin:(e,n)=>{const o={slug:e,status:"active"};return n&&(o.source=n),g(`${t}jetpack/v4/plugins`,a,{body:JSON.stringify(o)}).then(u).then(m)},activateAkismet:()=>g(`${t}jetpack/v4/plugins`,a,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(u).then(m),getAkismetData:()=>d(`${t}jetpack/v4/module/akismet/data`,s).then(u).then(m),checkAkismetKey:()=>d(`${t}jetpack/v4/module/akismet/key/check`,s).then(u).then(m),checkAkismetKeyTyped:e=>g(`${t}jetpack/v4/module/akismet/key/check`,a,{body:JSON.stringify({api_key:e})}).then(u).then(m),fetchStatsData:e=>d(function(e){let n=`${t}jetpack/v4/module/stats/data`;-1!==n.indexOf("?")?n+=`&range=${encodeURIComponent(e)}`:n+=`?range=${encodeURIComponent(e)}`;return n}(e),s).then(u).then(m).then(_),getPluginUpdates:()=>d(`${t}jetpack/v4/updates/plugins`,s).then(u).then(m),getPlans:()=>d(`${t}jetpack/v4/plans`,s).then(u).then(m),fetchSettings:()=>d(`${t}jetpack/v4/settings`,s).then(u).then(m),updateSetting:e=>g(`${t}jetpack/v4/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchSiteData:()=>d(`${t}jetpack/v4/site`,s).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>d(`${t}jetpack/v4/site/features`,s).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>d(`${t}jetpack/v4/site/products`,s).then(u).then(m),fetchSitePurchases:()=>d(`${t}jetpack/v4/site/purchases`,s).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>d(`${t}jetpack/v4/site/benefits`,s).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>d(`${t}jetpack/v4/site/discount`,s).then(u).then(m).then((e=>e.data)),fetchSetupQuestionnaire:()=>d(`${t}jetpack/v4/setup/questionnaire`,s).then(u).then(m),fetchRecommendationsData:()=>d(`${t}jetpack/v4/recommendations/data`,s).then(u).then(m),fetchRecommendationsProductSuggestions:()=>d(`${t}jetpack/v4/recommendations/product-suggestions`,s).then(u).then(m),fetchRecommendationsUpsell:()=>d(`${t}jetpack/v4/recommendations/upsell`,s).then(u).then(m),fetchRecommendationsConditional:()=>d(`${t}jetpack/v4/recommendations/conditional`,s).then(u).then(m),saveRecommendationsData:e=>g(`${t}jetpack/v4/recommendations/data`,a,{body:JSON.stringify({data:e})}).then(u),fetchProducts:()=>d(`${t}jetpack/v4/products`,s).then(u).then(m),fetchRewindStatus:()=>d(`${t}jetpack/v4/rewind`,s).then(u).then(m).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>d(`${t}jetpack/v4/scan`,s).then(u).then(m).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>g(`${t}jetpack/v4/notice/${e}`,a,{body:JSON.stringify({dismissed:!0})}).then(u).then(m),fetchPluginsData:()=>d(`${t}jetpack/v4/plugins`,s).then(u).then(m),fetchIntroOffers:()=>d(`${t}jetpack/v4/intro-offers`,s).then(u).then(m),fetchVerifySiteGoogleStatus:e=>d(null!==e?`${t}jetpack/v4/verify-site/google/${e}`:`${t}jetpack/v4/verify-site/google`,s).then(u).then(m),verifySiteGoogle:e=>g(`${t}jetpack/v4/verify-site/google`,a,{body:JSON.stringify({keyring_id:e})}).then(u).then(m),submitSurvey:e=>g(`${t}jetpack/v4/marketing/survey`,a,{body:JSON.stringify(e)}).then(u).then(m),saveSetupQuestionnaire:e=>g(`${t}jetpack/v4/setup/questionnaire`,a,{body:JSON.stringify(e)}).then(u).then(m),updateLicensingError:e=>g(`${t}jetpack/v4/licensing/error`,a,{body:JSON.stringify(e)}).then(u).then(m),updateLicenseKey:e=>g(`${t}jetpack/v4/licensing/set-license`,a,{body:JSON.stringify({license:e})}).then(u).then(m),getUserLicensesCounts:()=>d(`${t}jetpack/v4/licensing/user/counts`,s).then(u).then(m),getUserLicenses:()=>d(`${t}jetpack/v4/licensing/user/licenses`,s).then(u).then(m),updateLicensingActivationNoticeDismiss:e=>g(`${t}jetpack/v4/licensing/user/activation-notice-dismiss`,a,{body:JSON.stringify({last_detached_count:e})}).then(u).then(m),updateRecommendationsStep:e=>g(`${t}jetpack/v4/recommendations/step`,a,{body:JSON.stringify({step:e})}).then(u),confirmIDCSafeMode:()=>g(`${t}jetpack/v4/identity-crisis/confirm-safe-mode`,a).then(u),startIDCFresh:e=>g(`${t}jetpack/v4/identity-crisis/start-fresh`,a,{body:JSON.stringify({redirect_uri:e})}).then(u).then(m),migrateIDC:()=>g(`${t}jetpack/v4/identity-crisis/migrate`,a).then(u),attachLicenses:e=>g(`${t}jetpack/v4/licensing/attach-licenses`,a,{body:JSON.stringify({licenses:e})}).then(u).then(m),fetchSearchPlanInfo:()=>d(`${c}jetpack/v4/search/plan`,s).then(u).then(m),fetchSearchSettings:()=>d(`${c}jetpack/v4/search/settings`,s).then(u).then(m),updateSearchSettings:e=>g(`${c}jetpack/v4/search/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchSearchStats:()=>d(`${c}jetpack/v4/search/stats`,s).then(u).then(m),fetchWafSettings:()=>d(`${t}jetpack/v4/waf`,s).then(u).then(m),updateWafSettings:e=>g(`${t}jetpack/v4/waf`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchWordAdsSettings:()=>d(`${t}jetpack/v4/wordads/settings`,s).then(u).then(m),updateWordAdsSettings:e=>g(`${t}jetpack/v4/wordads/settings`,a,{body:JSON.stringify(e)}),fetchSearchPricing:()=>d(`${c}jetpack/v4/search/pricing`,s).then(u).then(m),fetchMigrationStatus:()=>d(`${t}jetpack/v4/migration/status`,s).then(u).then(m),fetchBackupUndoEvent:()=>d(`${t}jetpack/v4/site/backup/undo-event`,s).then(u).then(m),fetchBackupPreflightStatus:()=>d(`${t}jetpack/v4/site/backup/preflight`,s).then(u).then(m)};function d(e,n){return fetch(l(e),n)}function g(e,n,t){return fetch(e,Object.assign({},n,t)).catch(h)}function _(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,p)};function u(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new a})):e.json().catch((e=>g(e))).then((n=>{const t=new Error(`${n.message} (Status ${e.status})`);throw t.response=n,t.name="ApiError",t}))}function m(e){return e.json().catch((n=>g(n,e.redirected,e.url)))}function g(e,n,t){throw n?new s(t):new r}function h(){throw new p}},8089:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(6427),i=t(7723),c=t(2231),r=t(3619),s=t.n(r),a=t(1609),l=t.n(a),p=t(1112),d=t(7689);const __=i.__,u=e=>{const{label:n,onClick:t,isLoading:i=!1,loadingText:r,isDisabled:s,displayError:a=!1,errorMessage:u=__("An error occurred. Please try again.","jetpack-connection"),variant:m="primary",isExternalLink:g=!1,customClass:h}=e,_=r||l().createElement(o.Spinner,null);return l().createElement(l().Fragment,null,l().createElement(p.A,{className:(0,c.A)(d.A.button,"jp-action-button--button",h),label:n,onClick:t,variant:g?"link":m,isExternalLink:g,disabled:i||s},i?_:n),a&&l().createElement("p",{className:(0,c.A)(d.A.error,"jp-action-button__error")},u))};u.propTypes={label:s().string.isRequired,onClick:s().func,isLoading:s().bool,isDisabled:s().bool,displayError:s().bool,errorMessage:s().oneOfType([s().string,s().element]),variant:s().arrayOf(s().oneOf(["primary","secondary","link"])),isExternalLink:s().bool};const m=u},1112:(e,n,t)=>{"use strict";t.d(n,{A:()=>g});var o=t(8579),i=t.n(o),c=t(6427),r=t(7723),s=t(7750),a=t(8391),l=t(2231),p=t(1609),d=t.n(p),u=t(2258);const __=r.__,m=(0,p.forwardRef)(((e,n)=>{const{children:t,variant:o="primary",size:r="normal",weight:p="bold",icon:m,iconSize:g,disabled:h,isDestructive:_,isLoading:y,isExternalLink:f,className:b,text:k,fullWidth:C,...E}=e,v=(0,l.A)(u.A.button,b,{[u.A.normal]:"normal"===r,[u.A.small]:"small"===r,[u.A.icon]:Boolean(m),[u.A.loading]:y,[u.A.regular]:"regular"===p,[u.A["full-width"]]:C,[u.A["is-icon-button"]]:Boolean(m)&&!t});E.ref=n;const j="normal"===r?20:16,w=f&&d().createElement(d().Fragment,null,d().createElement(s.A,{size:j,icon:a.A,className:u.A["external-icon"]}),d().createElement(c.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-connection"))),A=f?"_blank":void 0,N=t?.[0]&&null!==t[0]&&"components-tooltip"!==t?.[0]?.props?.className;return d().createElement(c.Button,i()({target:A,variant:o,className:(0,l.A)(v,{"has-text":!!m&&N}),icon:f?void 0:m,iconSize:g,disabled:h,"aria-disabled":h,isDestructive:_,text:k},E),y&&d().createElement(c.Spinner,null),d().createElement("span",null,t),w)}));m.displayName="Button";const g=m},9121:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});t(9535);const o=({format:e="horizontal",icon:n,imageUrl:t})=>React.createElement("div",{className:"jp-components__decorative-card "+(e?"jp-components__decorative-card--"+e:"")},React.createElement("div",{className:"jp-components__decorative-card__image",style:{backgroundImage:t?`url( ${t} )`:""}}),React.createElement("div",{className:"jp-components__decorative-card__content"},React.createElement("div",{className:"jp-components__decorative-card__lines"})),n?React.createElement("div",{className:"jp-components__decorative-card__icon-container"},React.createElement("span",{className:"jp-components__decorative-card__icon jp-components__decorative-card__icon--"+n})):null)},7142:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(8579),i=t.n(o),c=t(7723),r=t(2231),s=t(1609),a=t.n(s);const __=c.__,l=({logoColor:e="#069e08",showText:n=!0,className:t,height:o=32,...c})=>{const s=n?"0 0 118 32":"0 0 32 32";return a().createElement("svg",i()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:s,className:(0,r.A)("jetpack-logo",t),"aria-labelledby":"jetpack-logo-title",height:o},c,{role:"img"}),a().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-connection")),a().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),n&&a().createElement(a().Fragment,null,a().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),a().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),a().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),a().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),a().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),a().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),a().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},442:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(9491),i=t(8403);const c=["sm","md","lg"],r=(e,n)=>{const t=Array.isArray(e)?e:[e],r=Array.isArray(n)?n:[n],[s,a,l]=c,p={sm:(0,o.useMediaQuery)(i.A[s]),md:(0,o.useMediaQuery)(i.A[a]),lg:(0,o.useMediaQuery)(i.A[l])};return t.map(((e,n)=>{const t=r[n];return t?((e,n,t)=>{const o=c.indexOf(e),i=o+1,r=n.includes("=");let s=[];return n.startsWith("<")&&(s=c.slice(0,r?i:o)),n.startsWith(">")&&(s=c.slice(r?o:i)),s?.length?s.some((e=>t[e])):t[e]})(e,t,p):p[e]}))}},1876:(e,n,t)=>{"use strict";t.d(n,{A:()=>s});var o=t(2231),i=t(1609),c=t.n(i),r=t(6406);const s=({children:e=null,width:n=null,height:t=null,className:i=""})=>c().createElement("div",{className:(0,o.A)(r.A.placeholder,i),style:{width:n,height:t}},e)},9957:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(4761),i=t(6427),c=t(7723),r=t(1876),s=t(5879);t(4319);const __=c.__,a=e=>-1===e.fraction.indexOf("00"),l=({currencyCode:e="USD",priceDetails:n=__("/month, paid yearly","jetpack-connection"),...t})=>{const l=(0,o.vA)(t.priceBefore,e),p=(0,o.vA)(t.priceAfter,e);return React.createElement("div",{className:"jp-components__pricing-card"},t.icon&&React.createElement("div",{className:"jp-components__pricing-card__icon"},"string"==typeof t.icon?React.createElement("img",{src:t.icon,alt:(0,c.sprintf)(/* translators: placeholder is a product name */
__("Icon for the product %s","jetpack-connection"),t.title)}):t.icon),React.createElement("h1",{className:"jp-components__pricing-card__title"},t.title),React.createElement("div",{className:"jp-components__pricing-card__pricing"},0===t.priceAfter&&React.createElement(r.A,{width:"100%",height:48}),t.priceBefore!==t.priceAfter&&t.priceAfter>0&&React.createElement("div",{className:"jp-components__pricing-card__price-before"},React.createElement("span",{className:"jp-components__pricing-card__currency"},l.symbol),React.createElement("span",{className:"jp-components__pricing-card__price"},l.integer),a(l)&&React.createElement("span",{className:"jp-components__pricing-card__price-decimal"}," ",l.fraction),React.createElement("div",{className:"jp-components__pricing-card__price-strikethrough"})),t.priceAfter>0&&React.createElement(React.Fragment,null,React.createElement("div",{className:"jp-components__pricing-card__price-after"},React.createElement("span",{className:"jp-components__pricing-card__currency"},p.symbol),React.createElement("span",{className:"jp-components__pricing-card__price"},p.integer),a(p)&&React.createElement("span",{className:"jp-components__pricing-card__price-decimal"},p.fraction)),React.createElement("span",{className:"jp-components__pricing-card__price-details"},n))),t.children&&React.createElement("div",{className:"jp-components__pricing-card__extra-content-wrapper"},t.children),t.tosText&&React.createElement("div",{className:"jp-components__pricing-card__tos"},t.tosText),t.ctaText&&React.createElement(React.Fragment,null,!t.tosText&&React.createElement("div",{className:"jp-components__pricing-card__tos"},React.createElement(s.A,{agreeButtonLabel:t.ctaText})),React.createElement("div",{className:"jp-components__pricing-card__cta"},React.createElement(i.Button,{className:"jp-components__pricing-card__button",label:t.ctaText,onClick:t.onCtaClick},t.ctaText))),t.infoText&&React.createElement("div",{className:"jp-components__pricing-card__info"},t.infoText))}},6461:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(3619),i=t.n(o),c=t(1609),r=t.n(c);t(8325);const s=({color:e="#FFFFFF",className:n="",size:t=20})=>{const o=n+" jp-components-spinner",i={width:t,height:t,fontSize:t,borderTopColor:e},c={borderTopColor:e,borderRightColor:e};return r().createElement("div",{className:o},r().createElement("div",{className:"jp-components-spinner__outer",style:i},r().createElement("div",{className:"jp-components-spinner__inner",style:c})))};s.propTypes={color:i().string,className:i().string,size:i().number};const a=s},5879:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(8579),i=t.n(o),c=t(6087),r=t(7723),s=t(2231),a=t(3924),l=t(7425);t(9634);const __=r.__,p=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,c.createInterpolateElement)((0,r.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),e[0],e[1]),{strong:React.createElement("strong",null),tosLink:React.createElement(u,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(u,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,c.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),{tosLink:React.createElement(u,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(u,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),d=({agreeButtonLabel:e})=>(0,c.createInterpolateElement)((0,r.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),e),{strong:React.createElement("strong",null),tosLink:React.createElement(u,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(u,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),u=({slug:e,children:n})=>React.createElement("a",{className:"terms-of-service__link",href:(0,a.A)(e),rel:"noopener noreferrer",target:"_blank"},n),m=({className:e,multipleButtons:n,agreeButtonLabel:t,...o})=>React.createElement(l.Ay,i()({className:(0,s.A)(e,"terms-of-service")},o),n?React.createElement(p,{multipleButtonsLabels:n}):React.createElement(d,{agreeButtonLabel:t}))},110:(e,n,t)=>{"use strict";t.d(n,{Q:()=>o,Z:()=>i});const o={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},i=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},7425:(e,n,t)=>{"use strict";t.d(n,{Ay:()=>d});var o=t(8579),i=t.n(o),c=t(2231),r=t(1609),s=t.n(r),a=t(110),l=t(4495);const p=(0,r.forwardRef)((({variant:e="body",children:n,component:t,className:o,...p},d)=>{const u=t||a.Q[e]||"span",m=(0,r.useMemo)((()=>a.Z.reduce(((e,n)=>(void 0!==p[n]&&(e+=l.A[`${n}-${p[n]}`]+" ",delete p[n]),e)),"")),[p]);return s().createElement(u,i()({className:(0,c.A)(l.A.reset,l.A[e],o,m)},p,{ref:d}),n)}));p.displayName="Text";const d=p},3924:(e,n,t)=>{"use strict";function o(e,n={}){const t={};let o;if("undefined"!=typeof window&&(o=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const n=new URL(e);e=`https://${n.host}${n.pathname}`,t.url=encodeURIComponent(e)}else t.source=encodeURIComponent(e);for(const e in n)t[e]=encodeURIComponent(n[e]);!Object.keys(t).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(t.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),o&&(t.calypso_env=o);return"https://jetpack.com/redirect/?"+Object.keys(t).map((e=>e+"="+t[e])).join("&")}t.d(n,{A:()=>o})},6439:(e,n,t)=>{let o={};try{o=t(9074)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),o={missingConfig:!0}}const i=e=>Object.hasOwn(o,e);e.exports={jetpackConfigHas:i,jetpackConfigGet:e=>{if(!i(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return o[e]}}},8421:(e,n,t)=>{"use strict";t.d(n,{A:()=>d});var o=t(8089),i=t(7723),c=t(3619),r=t.n(c),s=t(1609),a=t.n(s),l=t(9660);const __=i.__,p=e=>{const{apiRoot:n,apiNonce:t,connectLabel:i=__("Connect","jetpack-connection"),registrationNonce:c,redirectUri:r=null,from:s,autoTrigger:p=!1}=e,{handleRegisterSite:d,isRegistered:u,isUserConnected:m,siteIsRegistering:g,userIsConnecting:h,registrationError:_}=(0,l.A)({registrationNonce:c,redirectUri:r,apiRoot:n,apiNonce:t,autoTrigger:p,from:s});return a().createElement(a().Fragment,null,(!u||!m)&&a().createElement(o.A,{label:i,onClick:d,displayError:!!_,isLoading:g||h}))};p.propTypes={connectLabel:r().string,apiRoot:r().string.isRequired,apiNonce:r().string.isRequired,from:r().string,redirectUri:r().string.isRequired,registrationNonce:r().string.isRequired,autoTrigger:r().bool};const d=p},6212:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(7723),i=t(1609),c=t.n(i),r=t(9660),s=t(5582);const __=o.__,a=({title:e,buttonLabel:n,loadingLabel:t,apiRoot:o,apiNonce:i,registrationNonce:a,from:l,redirectUri:p,images:d,children:u,assetBaseUrl:m,autoTrigger:g,footer:h,skipUserConnection:_,skipPricingPage:y,logo:f})=>{const{handleRegisterSite:b,siteIsRegistering:k,userIsConnecting:C,registrationError:E,isOfflineMode:v}=(0,r.A)({registrationNonce:a,redirectUri:p,apiRoot:o,apiNonce:i,autoTrigger:g,from:l,skipUserConnection:_,skipPricingPage:y}),j=Boolean(E),w=k||C,A=E?.response?.code;return c().createElement(s.A,{title:e||__("Over 5 million WordPress sites are faster and more secure","jetpack-connection"),images:d||[],assetBaseUrl:m,buttonLabel:n||__("Set up Jetpack","jetpack-connection"),loadingLabel:t,handleButtonClick:b,displayButtonError:j,errorCode:A,buttonIsLoading:w,footer:h,isOfflineMode:v,logo:f},u)}},5582:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var o=t(3924),i=t(5879),c=t(8089),r=t(6087),s=t(7723),a=t(1609),l=t.n(a),p=t(2668);t(1772);const __=s.__,d=(e,n)=>{switch(e){case"fail_domain_forbidden":case"fail_ip_forbidden":case"fail_domain_tld":case"fail_subdomain_wpcom":case"siteurl_private_ip":return __("Your site host is on a private network. Jetpack can only connect to public sites.","jetpack-connection");case"connection_disabled":return __("This site has been suspended.","jetpack-connection")}if(n)return(0,r.createInterpolateElement)(__("Unavailable in <a>Offline Mode</a>","jetpack-connection"),{a:l().createElement("a",{href:(0,o.A)("jetpack-support-development-mode"),target:"_blank",rel:"noopener noreferrer"})})},u=({title:e,images:n,children:t,assetBaseUrl:o,isLoading:r,buttonLabel:s,handleButtonClick:a,displayButtonError:u,errorCode:m,buttonIsLoading:g,loadingLabel:h,footer:_,isOfflineMode:y,logo:f})=>l().createElement(p.A,{title:e,assetBaseUrl:o,images:n,className:"jp-connection__connect-screen"+(r?" jp-connection__connect-screen__loading":""),logo:f},l().createElement("div",{className:"jp-connection__connect-screen__content"},t,l().createElement("div",{className:"jp-connection__connect-screen__tos"},l().createElement(i.A,{agreeButtonLabel:s})),l().createElement(c.A,{label:s,onClick:a,displayError:u||y,errorMessage:d(m,y),isLoading:g,isDisabled:y}),l().createElement("span",{className:"jp-connection__connect-screen__loading-message",role:"status"},g?h||__("Loading","jetpack-connection"):""),_&&l().createElement("div",{className:"jp-connection__connect-screen__footer"},_)))},5745:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(1609),i=t.n(o);const c=({images:e,assetBaseUrl:n=""})=>{if(!e?.length)return null;const t=e.map(((e,t)=>i().createElement(i().Fragment,{key:t},i().createElement("img",{src:n+e,alt:""}))));return i().createElement("div",{className:"jp-connection__connect-screen__image-slider"},t)}},2668:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(7142),i=t(2231),c=t(1609),r=t.n(c),s=t(5745);t(9064);const a=({title:e,children:n,className:t,assetBaseUrl:c,images:a,logo:l,rna:p=!1})=>{const d=a?.length;return r().createElement("div",{className:(0,i.A)("jp-connection__connect-screen-layout",d?"jp-connection__connect-screen-layout__two-columns":"",t?" "+t:"")},p&&r().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs"},r().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs__green"}),r().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs__yellow"}),r().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs__blue"})),r().createElement("div",{className:"jp-connection__connect-screen-layout__left"},l||r().createElement(o.A,null),r().createElement("h2",null,e),n),d?r().createElement("div",{className:"jp-connection__connect-screen-layout__right"},r().createElement(s.A,{images:a,assetBaseUrl:c})):null)}},7945:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var o=t(7723),i=t(3619),c=t.n(i),r=t(1609),s=t.n(r),a=t(2558),l=t(9660),p=t(401);const __=o.__,d=e=>{const{title:n=__("Over 5 million WordPress sites are faster and more secure","jetpack-connection"),autoTrigger:t=!1,buttonLabel:o=__("Set up Jetpack","jetpack-connection"),apiRoot:i,apiNonce:c,registrationNonce:r,from:d,redirectUri:u,children:m,priceBefore:g,priceAfter:h,pricingIcon:_,pricingTitle:y,pricingCurrencyCode:f="USD",wpcomProductSlug:b,siteProductAvailabilityHandler:k,logo:C,rna:E=!1}=e,{handleRegisterSite:v,siteIsRegistering:j,userIsConnecting:w,registrationError:A,isOfflineMode:N}=(0,l.A)({registrationNonce:r,redirectUri:u,apiRoot:i,apiNonce:c,autoTrigger:t,from:d}),S=b||"",{run:R,hasCheckoutStarted:T}=(0,a.A)({productSlug:S,redirectUrl:u,siteProductAvailabilityHandler:k,from:d}),O=Boolean(A),I=j||w||T,P=S?R:v;return s().createElement(p.A,{title:n,buttonLabel:o,priceBefore:g,priceAfter:h,pricingIcon:_,pricingTitle:y,pricingCurrencyCode:f,handleButtonClick:P,displayButtonError:O,buttonIsLoading:I,logo:C,isOfflineMode:N,rna:E},m)};d.propTypes={title:c().string,buttonLabel:c().string,apiRoot:c().string.isRequired,apiNonce:c().string.isRequired,registrationNonce:c().string.isRequired,from:c().string,redirectUri:c().string.isRequired,autoTrigger:c().bool,pricingTitle:c().string.isRequired,pricingIcon:c().oneOfType([c().string,c().element]),priceBefore:c().number.isRequired,priceAfter:c().number.isRequired,pricingCurrencyCode:c().string,wpcomProductSlug:c().string,checkSiteHasWpcomProduct:c().func,logo:c().element};const u=d},401:(e,n,t)=>{"use strict";t.d(n,{A:()=>b});var o=t(8089),i=t(3924),c=t(9957),r=t(5879),s=t(6087),a=t(7723),l=t(2231),p=t(4804),d=t.n(p),u=t(3619),m=t.n(u),g=t(1609),h=t.n(g),_=t(2668);t(381);const __=a.__,y=d()("jetpack:connection:ConnectScreenRequiredPlanVisual"),f=e=>{const{title:n,buttonLabel:t,children:a,priceBefore:p,priceAfter:d,pricingIcon:u,pricingTitle:m,pricingCurrencyCode:g="USD",isLoading:f=!1,handleButtonClick:b=()=>{},displayButtonError:k=!1,buttonIsLoading:C=!1,logo:E,isOfflineMode:v,rna:j=!1}=e;y("props are %o",e);const w=(0,s.createInterpolateElement)(__("Already have a subscription? <connectButton/>","jetpack-connection"),{connectButton:h().createElement(o.A,{label:__("Log in to get started","jetpack-connection"),onClick:b,isLoading:C})}),A=v?(0,s.createInterpolateElement)(__("Unavailable in <a>Offline Mode</a>","jetpack-connection"),{a:h().createElement("a",{href:(0,i.A)("jetpack-support-development-mode"),target:"_blank",rel:"noopener noreferrer"})}):void 0;return h().createElement(_.A,{title:n,className:(0,l.A)("jp-connection__connect-screen-required-plan",f?"jp-connection__connect-screen-required-plan__loading":"",j?"rna":""),logo:E,rna:j},h().createElement("div",{className:"jp-connection__connect-screen-required-plan__content"},a,h().createElement("div",{className:"jp-connection__connect-screen-required-plan__pricing-card"},h().createElement(c.A,{title:m,icon:u,priceBefore:p,currencyCode:g,priceAfter:d},h().createElement(r.A,{agreeButtonLabel:t}),h().createElement(o.A,{label:t,onClick:b,displayError:k||v,errorMessage:A,isLoading:C,isDisabled:v}))),!v&&h().createElement("div",{className:"jp-connection__connect-screen-required-plan__with-subscription"},w)))};f.propTypes={pricingTitle:m().string.isRequired,priceBefore:m().number.isRequired,priceAfter:m().number.isRequired,pricingCurrencyCode:m().string,title:m().string,buttonLabel:m().string,pricingIcon:m().oneOfType([m().string,m().element]),isLoading:m().bool,handleButtonClick:m().func,displayButtonError:m().bool,buttonIsLoading:m().bool,logo:m().element,isOfflineMode:m().bool};const b=f},7840:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(5932),i=t(3619),c=t.n(i),r=t(1609);const s=e=>{const{redirectFunc:n=e=>window.location.assign(e),connectUrl:t,redirectUri:i=null,from:c}=e,[s,a]=(0,r.useState)(null);return t&&t!==s&&a(t),(0,r.useEffect)((()=>{s||o.Ay.fetchAuthorizationUrl(i).then((e=>a(e.authorizeUrl))).catch((e=>{throw e}))}),[]),s?(n(s+(c?(s.includes("?")?"&":"?")+"from="+encodeURIComponent(c):"")),null):null};s.propTypes={connectUrl:c().string,redirectUri:c().string.isRequired,from:c().string,redirectFunc:c().func};const a=s},648:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(7723),i=t(3619),c=t.n(i),r=t(1609),s=t.n(r),a=t(7499);const __=o.__,l=e=>{const{connectedPlugins:n,disconnectingPlugin:t}=e,o=(0,r.useMemo)((()=>{if(n){return Object.keys(n).map((e=>Object.assign({slug:e},n[e]))).filter((e=>t!==e.slug))}return[]}),[n,t]);return n&&o.length>0?s().createElement(s().Fragment,null,s().createElement("div",{className:"jp-connection__disconnect-dialog__step-copy"},s().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Jetpack is powering other plugins on your site. If you disconnect, these plugins will no longer work.","jetpack-connection"))),s().createElement("div",{className:"jp-connection__disconnect-card__group"},o.map((e=>s().createElement(a.A,{title:e.name,key:e.slug}))))):null};l.propTypes={connectedPlugins:c().array,disconnectingPlugin:c().string};const p=l},7088:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(442),i=t(6461),c=t(6427),r=t(7723),s=t(3619),a=t.n(s),l=t(1609),p=t.n(l),d=t(4175);const __=r.__,u=e=>{const{message:n,isRestoringConnection:t,restoreConnectionCallback:s,restoreConnectionError:a}=e,[l]=(0,o.A)(["md"],[">"]),u=d.A.notice+(l?" "+d.A["bigger-than-medium"]:""),m=p().createElement(c.Icon,{icon:p().createElement(c.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},p().createElement(c.Path,{d:"M11.7815 4.93772C11.8767 4.76626 12.1233 4.76626 12.2185 4.93772L20.519 19.8786C20.6116 20.0452 20.4911 20.25 20.3005 20.25H3.69951C3.50889 20.25 3.3884 20.0452 3.48098 19.8786L11.7815 4.93772Z",stroke:"#D63638",strokeWidth:"1.5"}),p().createElement(c.Path,{d:"M13 10H11V15H13V10Z",fill:"#D63638"}),p().createElement(c.Path,{d:"M13 16H11V18H13V16Z",fill:"#D63638"}))});if(!n)return null;if(t)return p().createElement(c.Notice,{status:"error",isDismissible:!1,className:u},p().createElement("div",{className:d.A.message},p().createElement(i.A,{color:"#B32D2E",size:24}),__("Reconnecting Jetpack","jetpack-connection")));const g=a?p().createElement(c.Notice,{status:"error",isDismissible:!1,className:u+" "+d.A.error},p().createElement("div",{className:d.A.message},m,(0,r.sprintf)(/* translators: placeholder is the error. */
__("There was an error reconnecting Jetpack. Error: %s","jetpack-connection"),a))):null;return p().createElement(p().Fragment,null,g,p().createElement(c.Notice,{status:"error",isDismissible:!1,className:u},p().createElement("div",{className:d.A.message},m,n),s&&p().createElement("a",{onClick:s,onKeyDown:s,className:d.A.button,href:"#"},__("Restore Connection","jetpack-connection"))))};u.propTypes={message:a().string.isRequired,restoreConnectionCallback:a().func,isRestoringConnection:a().bool,restoreConnectionError:a().string};const m=u},7499:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(3619),i=t.n(o),c=t(1609),r=t.n(c);t(7419);const s=e=>{const{title:n,value:t,description:o}=e;return r().createElement("div",{className:"jp-connection__disconnect-card card"},r().createElement("div",{className:"jp-connection__disconnect-card__card-content"},r().createElement("p",{className:"jp-connection__disconnect-card__card-headline"},n),(t||o)&&r().createElement("div",{className:"jp-connection__disconnect-card__card-stat-block"},r().createElement("span",{className:"jp-connection__disconnect-card__card-stat"},t),r().createElement("div",{className:"jp-connection__disconnect-card__card-description"},o))))};s.propTypes={title:i().string,value:i().oneOfType([i().string,i().number]),description:i().string};const a=s},3269:(e,n,t)=>{"use strict";t.d(n,{A:()=>y});var o=t(372),i=t(5932),c=t(6439),r=t(6427),s=t(7723),a=t(3619),l=t.n(a),p=t(1609),d=t.n(p),u=(t(785),t(4472)),m=t(8503),g=t(412),h=t(8090);const __=s.__,_=e=>{const[n,t]=(0,p.useState)(!1),[s,a]=(0,p.useState)(!1),[l,_]=(0,p.useState)(!1),[y,f]=(0,p.useState)(!1),[b,k]=(0,p.useState)(!1),[C,E]=(0,p.useState)(!1),{apiRoot:v,apiNonce:j,connectedPlugins:w,title:A=__("Are you sure you want to disconnect?","jetpack-connection"),pluginScreenDisconnectCallback:N,onDisconnected:S,onError:R,disconnectStepComponent:T,context:O="jetpack-dashboard",connectedUser:I={},connectedSiteId:P,isOpen:D,onClose:x}=e;let U="";(0,c.jetpackConfigHas)("consumer_slug")&&(U=(0,c.jetpackConfigGet)("consumer_slug"));const L=(0,p.useMemo)((()=>({context:O,plugin:U})),[O,U]);(0,p.useEffect)((()=>{i.Ay.setApiRoot(v),i.Ay.setApiNonce(j)}),[v,j]),(0,p.useEffect)((()=>{I&&I.ID&&I.login&&o.A.initialize(I.ID,I.login)}),[I,I.ID,I.login]),(0,p.useEffect)((()=>{D&&o.A.tracks.recordEvent("jetpack_disconnect_dialog_open",L)}),[D,L]),(0,p.useEffect)((()=>{D&&(s?!s||y||b?y&&!b?o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"survey"},L)):b&&o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"thank_you"},L)):o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"disconnect_confirm"},L)):o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"disconnect"},L)))}),[D,s,y,b,L]);const F=(0,p.useCallback)((()=>{i.Ay.disconnectSite().then((()=>{t(!1),a(!0)})).catch((e=>{t(!1),_(e),R&&R(e)}))}),[t,a,_,R]),B=(0,p.useCallback)(((e,n)=>{E(!0),fetch("https://public-api.wordpress.com/wpcom/v2/marketing/feedback-survey",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e)}).then((e=>e.json())).then((e=>{if(!0!==e.success)throw new Error("Survey endpoint returned error code "+e.code);o.A.tracks.recordEvent("jetpack_disconnect_survey_submit",n),k(!0),E(!1)})).catch((e=>{o.A.tracks.recordEvent("jetpack_disconnect_survey_error",Object.assign({},{error:e.message},n)),k(!0),E(!1)}))}),[E,k]),M=(0,p.useCallback)((e=>{e&&e.preventDefault(),_(!1),t(!0),"plugins"!==O?F():N&&N(e)}),[_,t,N,O,F]),$=(0,p.useCallback)((e=>o.A.tracks.recordEvent(e,L)),[L]),J=(0,p.useCallback)((()=>!(!I.ID||!P)),[I,P]),z=(0,p.useCallback)(((e,n,t)=>{if(t&&t.preventDefault(),!J())return void k(!0);const o={site_id:P,user_id:I.ID,survey_id:"jetpack-plugin-disconnect",survey_responses:{"why-cancel":{response:e,text:n||null}}},i=Object.assign({},L,{disconnect_reason:e});B(o,i)}),[B,k,J,P,I,L]),q=(0,p.useCallback)((e=>{e&&e.preventDefault(),S&&S(),x()}),[S,x]),G=(0,p.useCallback)((e=>{e&&e.preventDefault(),f(!0)}),[f]);return d().createElement(d().Fragment,null,D&&d().createElement(r.Modal,{title:"",contentLabel:A,aria:{labelledby:"jp-connection__disconnect-dialog__heading"},onRequestClose:x,shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissible:!1,className:"jp-connection__disconnect-dialog"+(s?" jp-connection__disconnect-dialog__success":"")},s?!s||y||b?y&&!b?d().createElement(g.A,{isSubmittingFeedback:C,onFeedBackProvided:z,onExit:q}):b?d().createElement(h.A,{onExit:q}):void 0:d().createElement(m.A,{canProvideFeedback:J(),onProvideFeedback:G,onExit:q}):d().createElement(u.A,{title:A,connectedPlugins:w,disconnectStepComponent:T,isDisconnecting:n,closeModal:x,onDisconnect:M,disconnectError:l,context:O,disconnectingPlugin:U,trackModalClick:$})))};_.propTypes={apiRoot:l().string.isRequired,apiNonce:l().string.isRequired,title:l().string,onDisconnected:l().func,onError:l().func,context:l().string,connectedPlugins:l().oneOfType([l().array,l().object]),pluginScreenDisconnectCallback:l().func,disconnectStepComponent:l().element,connectedUser:l().object,connectedSiteId:l().number,isOpen:l().bool,onClose:l().func};const y=_},8503:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(9121),i=t(6427),c=t(6087),r=t(7723),s=t(3619),a=t.n(s),l=t(1609),p=t.n(l),d=t(2365);const __=r.__,u=e=>{const{onExit:n,canProvideFeedback:t,onProvideFeedback:r}=e;return p().createElement("div",{className:"jp-connection__disconnect-dialog__content"},p().createElement(o.A,{icon:"unlink",imageUrl:d}),p().createElement("div",{className:"jp-connection__disconnect-dialog__step-copy jp-connection__disconnect-dialog__step-copy--narrow"},p().createElement("h1",null,(0,c.createInterpolateElement)(__("Jetpack has been <br/>successfully disconnected.","jetpack-connection"),{br:p().createElement("br",null)})),t&&p().createElement(p().Fragment,null,p().createElement("p",null,__("We’re sorry to see you go. Here at Jetpack, we’re always striving to provide the best experience for our customers. Please take our short survey (2 minutes, promise).","jetpack-connection")),p().createElement("p",null,p().createElement(i.Button,{variant:"primary",onClick:r,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},__("Help us improve","jetpack-connection"))),p().createElement("a",{className:"jp-connection__disconnect-dialog__link jp-connection__disconnect-dialog__link--bold",href:"#",onClick:n},__("No thank you","jetpack-connection"))),!t&&p().createElement(p().Fragment,null,p().createElement("p",null,p().createElement(i.Button,{variant:"primary",onClick:n,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},__("Back to my website","jetpack-connection"))))))};u.propTypes={onExit:a().func,onProvideFeedback:a().func,canProvideFeedback:a().bool};const m=u},4472:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(3924),i=t(6427),c=t(6087),r=t(7723),s=t(3619),a=t.n(s),l=t(1609),p=t.n(l),d=t(648);const __=r.__,u=e=>{const{title:n,isDisconnecting:t,onDisconnect:r,disconnectError:s,disconnectStepComponent:a,connectedPlugins:u,disconnectingPlugin:m,closeModal:g,context:h,trackModalClick:_}=e,y=(0,l.useCallback)((()=>_("jetpack_disconnect_dialog_click_learn_about")),[_]),f=(0,l.useCallback)((()=>_("jetpack_disconnect_dialog_click_support")),[_]),b=(0,l.useCallback)((()=>{_("jetpack_disconnect_dialog_click_stay_connected"),g()}),[_,g]),k=(0,l.useCallback)((e=>{_("jetpack_disconnect_dialog_click_disconnect"),r(e)}),[_,r]),C=(0,l.useCallback)((e=>{"Escape"!==e.key||t||b()}),[b,t]);(0,l.useEffect)((()=>(document.addEventListener("keydown",C,!1),()=>{document.removeEventListener("keydown",C,!1)})),[]);return p().createElement(p().Fragment,null,p().createElement("div",{className:"jp-connection__disconnect-dialog__content"},p().createElement("h1",{id:"jp-connection__disconnect-dialog__heading"},n),p().createElement(d.A,{connectedPlugins:u,disconnectingPlugin:m}),a,(()=>{if(!(u&&Object.keys(u).filter((e=>e!==m)).length)&&!a)return p().createElement("div",{className:"jp-connection__disconnect-dialog__step-copy"},p().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Jetpack is currently powering multiple products on your site.","jetpack-connection"),p().createElement("br",null),__("Once you disconnect Jetpack, these will no longer work.","jetpack-connection")))})()),p().createElement("div",{className:"jp-connection__disconnect-dialog__actions"},p().createElement("div",{className:"jp-row"},p().createElement("div",{className:"lg-col-span-8 md-col-span-9 sm-col-span-4"},p().createElement("p",null,(0,c.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <jpConnectionInfoLink>Jetpack connection</jpConnectionInfoLink> or <jpSupportLink>contact Jetpack support</jpSupportLink>.","jetpack-connection"),{strong:p().createElement("strong",null),jpConnectionInfoLink:p().createElement(i.ExternalLink,{href:(0,o.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__disconnect-dialog__link",onClick:y}),jpSupportLink:p().createElement(i.ExternalLink,{href:(0,o.A)("jetpack-support"),className:"jp-connection__disconnect-dialog__link",onClick:f})}))),p().createElement("div",{className:"jp-connection__disconnect-dialog__button-wrap lg-col-span-4 md-col-span-7 sm-col-span-4"},p().createElement(i.Button,{variant:"primary",disabled:t,onClick:b,className:"jp-connection__disconnect-dialog__btn-dismiss"},"plugins"===h?__("Cancel","jetpack-connection"):__("Stay connected","jetpack-connection",0)),(()=>{let e=__("Disconnect","jetpack-connection");return t?e=__("Disconnecting…","jetpack-connection"):"plugins"===h&&(e=__("Deactivate","jetpack-connection")),p().createElement(i.Button,{variant:"primary",disabled:t,onClick:k,className:"jp-connection__disconnect-dialog__btn-disconnect"},e)})())),s&&p().createElement("p",{className:"jp-connection__disconnect-dialog__error"},s)))};u.propTypes={title:a().string,isDisconnecting:a().bool,onDisconnect:a().func,disconnectError:a().bool,disconnectStepComponent:a().element,connectedPlugins:a().array,disconnectingPlugin:a().string,closeModal:a().func,context:a().string,trackModalClick:a().func};const m=u},412:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(7723),i=t(3619),c=t.n(i),r=t(1609),s=t.n(r),a=(t(255),t(2951));const __=o.__,l=e=>{const{onExit:n,onFeedBackProvided:t,isSubmittingFeedback:o}=e;return s().createElement("div",{className:"jp-connection__disconnect-dialog__content"},s().createElement("h1",null,__("Before you go, help us improve Jetpack","jetpack-connection")),s().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Let us know what didn‘t work for you","jetpack-connection")),s().createElement(a.A,{onSubmit:t,isSubmittingFeedback:o}),s().createElement("a",{className:"jp-connection__disconnect-dialog__link jp-connection__disconnect-dialog__link--bold",href:"#",onClick:n},__("Skip for now","jetpack-connection")))};l.PropTypes={onExit:c().func,onFeedBackProvided:c().func,isSubmittingFeedback:c().bool};const p=l},8090:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(9121),i=t(6427),c=t(6087),r=t(7723),s=t(3619),a=t.n(s),l=t(1609),p=t.n(l),d=t(9362);const __=r.__,u=e=>{const{onExit:n}=e;return p().createElement("div",{className:"jp-connection__disconnect-dialog__content"},p().createElement(o.A,{format:"vertical",imageUrl:d}),p().createElement("div",{className:"jp-connection__disconnect-dialog__copy"},p().createElement("h1",null,__("Thank you!","jetpack-connection")),p().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},(0,c.createInterpolateElement)(__("Your answer has been submitted. <br/>Thanks for your input on how we can improve Jetpack.","jetpack-connection"),{br:p().createElement("br",null)})),p().createElement(i.Button,{variant:"primary",onClick:n,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},__("Back to my website","jetpack-connection"))))};u.PropTypes={onExit:a().func,assetBaseUrl:a().string};const m=u},2951:(e,n,t)=>{"use strict";t.d(n,{A:()=>d});var o=t(6427),i=t(7723),c=t(3619),r=t.n(c),s=t(1609),a=t.n(s),l=t(8233);const __=i.__,p=e=>{const{onSubmit:n,isSubmittingFeedback:t}=e,[i,c]=(0,s.useState)(),[r,p]=(0,s.useState)(),d=[{id:"troubleshooting",answerText:__("Troubleshooting - I'll be reconnecting afterwards.","jetpack-connection")},{id:"not-working",answerText:__("I can't get it to work.","jetpack-connection")},{id:"slowed-down-site",answerText:__("It slowed down my site.","jetpack-connection")},{id:"buggy",answerText:__("It's buggy.","jetpack-connection")},{id:"what-does-it-do",answerText:__("I don't know what it does.","jetpack-connection")}],u="another-reason",m=(0,s.useCallback)((()=>{n(i,i===u?r:"")}),[n,u,r,i]),g=(0,s.useCallback)((e=>{const n=e.target.value;e.stopPropagation(),p(n)}),[p]),h=e=>e===i?"jp-connect__disconnect-survey-card--selected":"",_=(0,s.useCallback)(((e,n)=>{switch(n.key){case"Enter":case"Space":case"Spacebar":case" ":c(e)}}),[c]);return a().createElement(a().Fragment,null,a().createElement("div",{className:"jp-connection__disconnect-dialog__survey"},d.map((e=>a().createElement(l.A,{key:e.id,id:e.id,onClick:c,onKeyDown:_,className:"card jp-connect__disconnect-survey-card "+h(e.id)},a().createElement("p",{className:"jp-connect__disconnect-survey-card__answer"},e.answerText)))),a().createElement(l.A,{id:u,key:u,onClick:c,onKeyDown:_,className:"card jp-connect__disconnect-survey-card "+h(u)},a().createElement("p",{className:"jp-connect__disconnect-survey-card__answer"},__("Other:","jetpack-connection")," ",a().createElement("input",{placeholder:__("share your experience","jetpack-connection"),className:"jp-connect__disconnect-survey-card__input",type:"text",value:r,onChange:g,maxLength:1e3})))),a().createElement("p",null,a().createElement(o.Button,{disabled:!i||t,variant:"primary",onClick:m,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},t?__("Submitting…","jetpack-connection"):__("Submit Feedback","jetpack-connection",0))))};p.PropTypes={onSubmit:r().func,isSubmittingFeedback:r().bool};const d=p},8233:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(1609),i=t.n(o);t(255);const c=e=>{const{id:n,onClick:t,onKeyDown:c,children:r,className:s}=e,a=(0,o.useCallback)((()=>{t(n)}),[n,t]),l=(0,o.useCallback)((e=>{c(n,e)}),[n,c]);return i().createElement("div",{tabIndex:"0",role:"button",onClick:a,onKeyDown:l,className:"card jp-connect__disconnect-survey-card "+s},r)}},7018:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(7723),i=t(3619),c=t.n(i),r=t(1609),s=t.n(r);t(3732);const __=o.__,a=e=>{const{title:n,isLoading:t=!1,width:o="100%",displayTOS:i,scrollToIframe:c=!1,connectUrl:a,onComplete:l,onThirdPartyCookiesBlocked:p,location:d}=e;let{height:u="300"}=e;const m=(0,r.useRef)(void 0),g=(0,r.useRef)(void 0),h=e=>{if(g.current&&e.source===g.current.contentWindow)switch(e.data){case"close":window.removeEventListener("message",h),l&&l();break;case"wpcom_nocookie":p&&p()}};(0,r.useEffect)((()=>{c&&window.scrollTo(0,m.current.offsetTop-10),window.addEventListener("message",h)}));let _=a.replace("authorize/","authorize_iframe/");return _.includes("?")||(_+="?"),i&&(_+="&display-tos",u=(parseInt(u)+50).toString()),_+="&iframe_height="+parseInt(u),d&&(_+="&iframe_source="+d),s().createElement("div",{className:"dops-card fade-in jp-iframe-wrap",ref:m},s().createElement("h1",null,n),t?s().createElement("p",null,__("Loading…","jetpack-connection")):s().createElement("iframe",{title:n,width:o,height:u,src:_,ref:g}))};a.propTypes={title:c().string.isRequired,isLoading:c().bool,width:c().string,height:c().string,connectUrl:c().string.isRequired,displayTOS:c().bool.isRequired,scrollToIframe:c().bool,onComplete:c().func,onThirdPartyCookiesBlocked:c().func,location:c().string};const l=a},4981:(e,n,t)=>{"use strict";t.d(n,{A:()=>C});var o=t(7425),i=t(3924),c=t(1112),r=t(6427),s=t(6087),a=t(7723),l=t(7750),p=t(1386),d=t(8391),u=t(2231),m=t(3619),g=t.n(m),h=t(1609),_=t.n(h),y=t(3269);t(2057);const __=a.__,f=e=>{const{title:n=__("Manage your Jetpack connection","jetpack-connection"),apiRoot:t,apiNonce:c,connectedPlugins:s,onDisconnected:a,context:l="jetpack-dashboard",connectedUser:p={},connectedSiteId:d,isOpen:u=!1,onClose:m}=e,[g,f]=(0,h.useState)(!1),C=(0,h.useCallback)((e=>{e&&e.preventDefault(),f(!0)}),[f]),E=(0,h.useCallback)((e=>{e&&e.preventDefault(),f(!1)}),[f]);return _().createElement(_().Fragment,null,u&&_().createElement(_().Fragment,null,_().createElement(r.Modal,{title:"",contentLabel:n,aria:{labelledby:"jp-connection__manage-dialog__heading"},shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissible:!1,className:"jp-connection__manage-dialog"},_().createElement("div",{className:"jp-connection__manage-dialog__content"},_().createElement("h1",{id:"jp-connection__manage-dialog__heading"},n),_().createElement(o.Ay,{className:"jp-connection__manage-dialog__large-text"},__("At least one user must be connected for your Jetpack products to work properly.","jetpack-connection")),_().createElement(b,{title:__("Transfer ownership to another admin","jetpack-connection"),link:(0,i.A)("calypso-settings-manage-connection",{site:window?.myJetpackInitialState?.siteSuffix}),key:"transfer",action:"transfer"}),_().createElement(b,{title:__("Disconnect Jetpack","jetpack-connection"),onClick:C,key:"disconnect",action:"disconnect"})),_().createElement(k,{onClose:m})),_().createElement(y.A,{apiRoot:t,apiNonce:c,onDisconnected:a,connectedPlugins:s,connectedSiteId:d,connectedUser:p,isOpen:g,onClose:E,context:l})))},b=({title:e,onClick:n=()=>null,link:t="#",action:o})=>_().createElement("div",{className:"jp-connection__manage-dialog__action-card card"},_().createElement("div",{className:"jp-connection__manage-dialog__action-card__card-content"},_().createElement("a",{href:t,className:(0,u.A)("jp-connection__manage-dialog__action-card__card-headline",o),onClick:n},e,_().createElement(l.A,{icon:"disconnect"===o?p.A:d.A,className:"jp-connection__manage-dialog__action-card__icon"})))),k=({onClose:e})=>_().createElement("div",{className:"jp-row jp-connection__manage-dialog__actions"},_().createElement("div",{className:"jp-connection__manage-dialog__text-wrap lg-col-span-9 md-col-span-7 sm-col-span-3"},_().createElement(o.Ay,null,(0,s.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <connectionInfoLink>Jetpack connection</connectionInfoLink> or <supportLink>contact Jetpack support</supportLink>","jetpack-connection"),{strong:_().createElement("strong",null),connectionInfoLink:_().createElement(r.ExternalLink,{href:(0,i.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__manage-dialog__link"}),supportLink:_().createElement(r.ExternalLink,{href:(0,i.A)("jetpack-support"),className:"jp-connection__manage-dialog__link"})}))),_().createElement("div",{className:"jp-connection__manage-dialog__button-wrap lg-col-span-3 md-col-span-1 sm-col-span-1"},_().createElement(c.A,{weight:"regular",variant:"secondary",onClick:e,className:"jp-connection__manage-dialog__btn-dismiss"},__("Cancel","jetpack-connection"))));f.propTypes={title:g().string,apiRoot:g().string.isRequired,apiNonce:g().string.isRequired,connectedPlugins:g().oneOfType([g().array,g().object]),onDisconnected:g().func,context:g().string,connectedUser:g().object,connectedSiteId:g().number,isOpen:g().bool,onClose:g().func};const C=f},9660:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(5932),i=t(7999),c=t(7143),r=t(1609),s=t(4293);const a=window?.JP_CONNECTION_INITIAL_STATE||(0,i.getScriptData)()?.connection||{},l=({registrationNonce:e=a.registrationNonce,apiRoot:n=a.apiRoot,apiNonce:t=a.apiNonce,redirectUri:i,autoTrigger:l,from:p,skipUserConnection:d,skipPricingPage:u}={})=>{const{registerSite:m,connectUser:g,refreshConnectedPlugins:h}=(0,c.useDispatch)(s.a),_=(0,c.useSelect)((e=>e(s.a).getRegistrationError())),{siteIsRegistering:y,userIsConnecting:f,userConnectionData:b,connectedPlugins:k,connectionErrors:C,isRegistered:E,isUserConnected:v,hasConnectedOwner:j,isOfflineMode:w}=(0,c.useSelect)((e=>({siteIsRegistering:e(s.a).getSiteIsRegistering(),userIsConnecting:e(s.a).getUserIsConnecting(),userConnectionData:e(s.a).getUserConnectionData(),connectedPlugins:e(s.a).getConnectedPlugins(),connectionErrors:e(s.a).getConnectionErrors(),isOfflineMode:e(s.a).getIsOfflineMode(),...e(s.a).getConnectionStatus()}))),A=()=>d?i?(window.location=i,Promise.resolve(i)):Promise.resolve():g({from:p,redirectUri:i,skipPricingPage:u}),N=n=>(n&&n.preventDefault(),E?A():m({registrationNonce:e,redirectUri:i,from:p}).then((()=>A())));return(0,r.useEffect)((()=>{o.Ay.setApiRoot(n),o.Ay.setApiNonce(t)}),[n,t]),(0,r.useEffect)((()=>{!l||y||f||N()}),[]),{handleRegisterSite:N,handleConnectUser:A,refreshConnectedPlugins:h,isRegistered:E,isUserConnected:v,siteIsRegistering:y,userIsConnecting:f,registrationError:_,userConnectionData:b,hasConnectedOwner:j,connectedPlugins:k,connectionErrors:C,isOfflineMode:w}}},3765:(e,n,t)=>{"use strict";t.d(n,{A:()=>i});var o=t(7999);function i(){const e=("undefined"!=typeof window&&window?.JP_CONNECTION_INITIAL_STATE||(0,o.getScriptData)()?.connection)?.calypsoEnv;switch(e){case"development":return"http://calypso.localhost:3000/";case"wpcalypso":return"https://wpcalypso.wordpress.com/";case"horizon":return"https://horizon.wordpress.com/";default:return"https://wordpress.com/"}}},4617:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o=e=>{window.location.replace(e)}},9628:(e,n,t)=>{"use strict";t.d(n,{A:()=>r,R:()=>s});var o=t(7088),i=t(9660),c=t(1713);function r(){const{connectionErrors:e}=(0,i.A)({}),n=Object.values(e).shift(),t=n&&Object.values(n).length&&Object.values(n).shift().error_message;return{hasConnectionError:Boolean(t),connectionErrorMessage:t}}const s=()=>{const{hasConnectionError:e,connectionErrorMessage:n}=r(),{restoreConnection:t,isRestoringConnection:i,restoreConnectionError:s}=(0,c.A)();return e?React.createElement(o.A,{isRestoringConnection:i,restoreConnectionError:s,restoreConnectionCallback:t,message:n}):null}},2558:(e,n,t)=>{"use strict";t.d(n,{A:()=>f});var o=t(5932),i=t(7999),c=t(7143),r=t(4804),s=t.n(r),a=t(1609),l=t(3765),p=t(9660),d=t(4293);const u=s()("jetpack:connection:useProductCheckoutWorkflow"),{registrationNonce:m,apiRoot:g,apiNonce:h,siteSuffix:_}=window?.JP_CONNECTION_INITIAL_STATE||(0,i.getScriptData)()?.connection||{},y=()=>"undefined"!=typeof window?window?.myJetpackInitialState?.adminUrl:null;function f({productSlug:e,redirectUrl:n,siteSuffix:t=_,adminUrl:i=y(),connectAfterCheckout:r=!1,siteProductAvailabilityHandler:s=null,quantity:f=null,from:b,useBlogIdSuffix:k=!1}={}){u("productSlug is %s",e),u("redirectUrl is %s",n),u("siteSuffix is %s",t),u("from is %s",b);const[C,E]=(0,a.useState)(!1),{registerSite:v}=(0,c.useDispatch)(d.a),j=(0,c.useSelect)((e=>e(d.a).getBlogId()),[]);u("blogID is %s",j??"undefined"),k=k&&!!j;const{isUserConnected:w,isRegistered:A,handleConnectUser:N}=(0,p.A)({redirectUri:n,from:b}),S=(0,a.useMemo)((()=>{const o=(0,l.A)(),c=(!A||!w)&&r,s=c?"checkout/jetpack/":`checkout/${k?j.toString():t}/`,a=new URL(`${o}${s}${e}${null!=f?`:-q-${f}`:""}`);return c?(a.searchParams.set("connect_after_checkout",!0),a.searchParams.set("admin_url",i),a.searchParams.set("from_site_slug",t)):a.searchParams.set("site",t),a.searchParams.set("source",b),a.searchParams.set("redirect_to",n),w||a.searchParams.set("unlinked","1"),a}),[A,w,r,t,f,e,b,n,i,k,j]);u("isRegistered is %s",A),u("isUserConnected is %s",w),u("connectAfterCheckout is %s",r),u("checkoutUrl is %s",S);const R=(e=null)=>Promise.resolve(s&&s()).then((n=>{if(e&&S.searchParams.set("redirect_to",e),n)return u("handleAfterRegistration: Site has a product associated"),N();u("handleAfterRegistration: Site does not have a product associated. Redirecting to checkout %s",S),window.location.href=S}));return(0,a.useEffect)((()=>{o.Ay.setApiRoot(g),o.Ay.setApiNonce(h)}),[]),{run:(e,t=null)=>(e&&e.preventDefault(),E(!0),r?((e=null)=>{e&&S.searchParams.set("redirect_to",e),u("Redirecting to connectAfterCheckout flow: %s",S),window.location.href=S})(t):A?R(t):void v({registrationNonce:m,redirectUri:n}).then((()=>R(t)))),isRegistered:A,hasCheckoutStarted:C}}},1713:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(5932),i=t(7999),c=t(7143),r=t(1609),s=t(4293);const{apiRoot:a,apiNonce:l}=window?.JP_CONNECTION_INITIAL_STATE||(0,i.getScriptData)()?.connection||{};function p(){const[e,n]=(0,r.useState)(!1),[t,i]=(0,r.useState)(null),{disconnectUserSuccess:p,setConnectionErrors:d}=(0,c.useDispatch)(s.a);return(0,r.useEffect)((()=>{o.Ay.setApiRoot(a),o.Ay.setApiNonce(l)}),[]),{restoreConnection:(e=!0)=>(n(!0),i(null),o.Ay.reconnect().then((n=>("in_progress"===n.status?(p(),d({}),e&&(window.location.href="/wp-admin/admin.php?page=my-jetpack#/connection")):window.location.reload(),n))).catch((e=>{throw i(e),n(!1),e}))),isRestoringConnection:e,restoreConnectionError:t}}},8980:(e,n,t)=>{"use strict";t.d(n,{AY:()=>g.A,F0:()=>o.A,Hx:()=>y.a,JC:()=>l.A,Jl:()=>i.A,Ni:()=>u.A,Ob:()=>b.A,Rc:()=>p.R,Sx:()=>p.A,ag:()=>_.A,bo:()=>d.A,cS:()=>f.A,d1:()=>h.A,mX:()=>a.A,nM:()=>c.A,pK:()=>r.A,w5:()=>m.A,xW:()=>s.A});var o=t(6212),i=t(2668),c=t(7945),r=t(8421),s=t(7018),a=t(7840),l=t(7088),p=t(9628),d=t(3269),u=t(7499),m=t(9660),g=t(4981),h=t(4617),_=t(3765),y=t(4293),f=t(2558),b=t(1713)},3935:(e,n,t)=>{"use strict";t.d(n,{A1:()=>a,Ay:()=>E,DO:()=>s,Ij:()=>r,Kl:()=>m,LW:()=>l,MU:()=>g,XY:()=>p,ZO:()=>c,dz:()=>d,gH:()=>u,v_:()=>i});var o=t(5932);const i="SET_CONNECTION_STATUS",c="SET_CONNECTION_STATUS_IS_FETCHING",r="SET_SITE_IS_REGISTERING",s="SET_USER_IS_CONNECTING",a="SET_REGISTRATION_ERROR",l="CLEAR_REGISTRATION_ERROR",p="SET_AUTHORIZATION_URL",d="DISCONNECT_USER_SUCCESS",u="SET_CONNECTED_PLUGINS",m="SET_CONNECTION_ERRORS",g="SET_IS_OFFLINE_MODE",h=e=>({type:i,connectionStatus:e}),_=e=>({type:r,isRegistering:e}),y=e=>({type:s,isConnecting:e}),f=e=>({type:a,registrationError:e}),b=()=>({type:l}),k=e=>({type:p,authorizationUrl:e}),C=e=>({type:u,connectedPlugins:e});const E={setConnectionStatus:h,setConnectionStatusIsFetching:e=>({type:c,isFetching:e}),fetchConnectionStatus:()=>({type:"FETCH_CONNECTION_STATUS"}),fetchAuthorizationUrl:e=>({type:"FETCH_AUTHORIZATION_URL",redirectUri:e}),setSiteIsRegistering:_,setUserIsConnecting:y,setRegistrationError:f,clearRegistrationError:b,setAuthorizationUrl:k,registerSite:function*({registrationNonce:e,redirectUri:n,from:t=""}){yield b(),yield _(!0);try{const o=yield{type:"REGISTER_SITE",registrationNonce:e,redirectUri:n,from:t};return yield h({isRegistered:!0}),yield k(o.authorizeUrl),yield _(!1),Promise.resolve(o)}catch(e){return yield f(e),yield _(!1),Promise.reject(e)}},connectUser:function*({from:e,redirectFunc:n,redirectUri:t,skipPricingPage:o}={}){yield y(!0),yield{type:"CONNECT_USER",from:e,redirectFunc:n,redirectUri:t,skipPricingPage:o}},disconnectUserSuccess:()=>({type:d}),setConnectedPlugins:C,refreshConnectedPlugins:()=>async({dispatch:e})=>await new Promise((n=>o.Ay.fetchConnectedPlugins().then((t=>{e(C(t)),n(t)})))),setConnectionErrors:e=>({type:m,connectionErrors:e}),setIsOfflineMode:e=>({type:g,isOfflineMode:e})}},2494:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(5932),i=t(7143),c=t(2279);const r={FETCH_AUTHORIZATION_URL:({redirectUri:e})=>o.Ay.fetchAuthorizationUrl(e),REGISTER_SITE:({registrationNonce:e,redirectUri:n,from:t})=>o.Ay.registerSite(e,n,t),CONNECT_USER:(0,i.createRegistryControl)((({resolveSelect:e})=>({from:n,redirectFunc:t,redirectUri:o,skipPricingPage:i}={})=>new Promise(((r,s)=>{e(c.A).getAuthorizationUrl(o).then((e=>{const o=t||(e=>window.location.assign(e)),c=new URL(e);i&&c.searchParams.set("skip_pricing","true"),n&&c.searchParams.set("from",encodeURIComponent(n));const s=c.toString();o(s),r(s)})).catch((e=>{s(e)}))}))))}},5051:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(7143),i=t(3935);const c=(0,o.combineReducers)({connectionStatus:(e={},n)=>{switch(n.type){case i.v_:return{...e,...n.connectionStatus};case i.dz:return{...e,isUserConnected:!1}}return e},connectionStatusIsFetching:(e=!1,n)=>n.type===i.ZO?n.isFetching:e,siteIsRegistering:(e=!1,n)=>n.type===i.Ij?n.isRegistering:e,userIsConnecting:(e=!1,n)=>n.type===i.DO?n.isConnecting:e,registrationError:(e,n)=>{switch(n.type){case i.LW:return!1;case i.A1:return n.registrationError;default:return e}},authorizationUrl:(e,n)=>n.type===i.XY?n.authorizationUrl:e,userConnectionData:(e,n)=>(n.type,e),connectedPlugins:(e={},n)=>n.type===i.gH?n.connectedPlugins:e,connectionErrors:(e={},n)=>n.type===i.Kl?n.connectionErrors:e,isOfflineMode:(e=!1,n)=>n.type===i.MU?n.isConnecting:e})},8019:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(7143),i=t(3935),c=t(2279);const r={...{getAuthorizationUrl:{isFulfilled:(e,...n)=>{const t=Boolean(e.authorizationUrl),i=(0,o.select)(c.A).hasFinishedResolution("getAuthorizationUrl",n);return t&&!i&&(0,o.dispatch)(c.A).finishResolution("getAuthorizationUrl",n),t},*fulfill(e){const n=yield i.Ay.fetchAuthorizationUrl(e);yield i.Ay.setAuthorizationUrl(n.authorizeUrl)}}}}},2676:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={...{getConnectionStatus:e=>e.connectionStatus||{},getConnectionStatusIsFetching:()=>!1,getSiteIsRegistering:e=>e.siteIsRegistering||!1,getUserIsConnecting:e=>e.userIsConnecting||!1,getRegistrationError:e=>e.registrationError||!1,getAuthorizationUrl:e=>e.authorizationUrl||!1,getUserConnectionData:e=>e.userConnectionData||!1,getConnectedPlugins:e=>e.connectedPlugins||[],getConnectionErrors:e=>e.connectionErrors||[],getIsOfflineMode:e=>e.isOfflineMode||!1,getWpcomUser:e=>e?.userConnectionData?.currentUser?.wpcomUser,getBlogId:e=>e?.userConnectionData?.currentUser?.blogId}}},8734:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(7143);class i{static store=null;static mayBeInit(e,n){null===i.store&&(i.store=(0,o.createReduxStore)(e,n),(0,o.register)(i.store))}}const c=i},2279:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o="jetpack-connection"},4293:(e,n,t)=>{"use strict";t.d(n,{a:()=>p.A});var o=t(7999),i=t(3935),c=t(2494),r=t(5051),s=t(8019),a=t(2676),l=t(8734),p=t(2279);const d=window.JP_CONNECTION_INITIAL_STATE||(0,o.getScriptData)()?.connection;d||console.error("Jetpack Connection package: Initial state is missing. Check documentation to see how to use the Connection composer package to set up the initial state."),l.A.mayBeInit(p.A,{__experimentalUseThunks:!0,reducer:r.A,actions:i.Ay,selectors:a.A,resolvers:s.A,controls:c.A,initialState:d||{}})},2365:(e,n,t)=>{"use strict";e.exports=t.p+"images/disconnect-confirm-dc9fe8f5c68cfd1320e0.jpg"},9362:(e,n,t)=>{"use strict";e.exports=t.p+"images/disconnect-thanks-5873bfac56a9bd7322cd.jpg"},9074:e=>{"use strict";e.exports={consumer_slug:"connection_package"}},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},8579:e=>{function n(){return e.exports=n=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)({}).hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,n.apply(null,arguments)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},2231:(e,n,t)=>{"use strict";function o(e){var n,t,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var c=e.length;for(n=0;n<c;n++)e[n]&&(t=o(e[n]))&&(i&&(i+=" "),i+=t)}else for(t in e)e[t]&&(i&&(i+=" "),i+=t);return i}t.d(n,{A:()=>i});const i=function(){for(var e,n,t=0,i="",c=arguments.length;t<c;t++)(e=arguments[t])&&(n=o(e))&&(i&&(i+=" "),i+=n);return i}}},n={};function t(o){var i=n[o];if(void 0!==i)return i.exports;var c=n[o]={exports:{}};return e[o](c,c.exports,t),c.exports}t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;t.g.importScripts&&(e=t.g.location+"");var n=t.g.document;if(!e&&n&&(n.currentScript&&"SCRIPT"===n.currentScript.tagName.toUpperCase()&&(e=n.currentScript.src),!e)){var o=n.getElementsByTagName("script");if(o.length)for(var i=o.length-1;i>-1&&(!e||!/^http(s?):/.test(e));)e=o[i--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),t.p=e})();var o={};return(()=>{"use strict";t.r(o),t.d(o,{CONNECTION_STORE_ID:()=>e.Hx,ConnectButton:()=>e.pK,ConnectScreen:()=>e.F0,ConnectScreenLayout:()=>e.Jl,ConnectScreenRequiredPlan:()=>e.nM,ConnectUser:()=>e.mX,ConnectionError:()=>e.Rc,ConnectionErrorNotice:()=>e.JC,DisconnectCard:()=>e.Ni,DisconnectDialog:()=>e.bo,InPlaceConnection:()=>e.xW,ManageConnectionDialog:()=>e.AY,getCalypsoOrigin:()=>e.ag,thirdPartyCookiesFallbackHelper:()=>e.d1,useConnection:()=>e.w5,useConnectionErrorNotice:()=>e.Sx,useProductCheckoutWorkflow:()=>e.cS,useRestoreConnection:()=>e.Ob});var e=t(8980)})(),o})()));