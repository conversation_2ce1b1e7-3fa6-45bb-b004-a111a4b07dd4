<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Soundasleep\\' => array($vendorDir . '/soundasleep/html2text/src'),
    'Sabberworm\\CSS\\' => array($vendorDir . '/sabberworm/php-css-parser/src'),
    'Pelago\\Emogrifier\\' => array($vendorDir . '/pelago/emogrifier/src'),
    'Opis\\Uri\\' => array($vendorDir . '/opis/uri/src'),
    'Opis\\String\\' => array($vendorDir . '/opis/string/src'),
    'Opis\\JsonSchema\\' => array($vendorDir . '/opis/json-schema/src'),
    'MaxMind\\Db\\' => array($vendorDir . '/maxmind-db/reader/src/MaxMind/Db'),
    'Composer\\Installers\\' => array($vendorDir . '/composer/installers/src/Composer/Installers'),
    'Automattic\\WooCommerce\\Vendor\\' => array($baseDir . '/lib/packages'),
    'Automattic\\WooCommerce\\Blueprint\\' => array($baseDir . '/packages/blueprint/src'),
    'Automattic\\WooCommerce\\' => array($baseDir . '/src'),
    'Automattic\\Jetpack\\Autoloader\\' => array($vendorDir . '/automattic/jetpack-autoloader/src'),
);
