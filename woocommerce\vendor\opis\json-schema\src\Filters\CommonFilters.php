<?php
/* ============================================================================
 * Copyright 2020 Zindex Software
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ============================================================================ */

namespace Opis\JsonSchema\Filters;

final class CommonFilters
{
    public static function Regex(string $value, array $args): bool
    {
        if (!isset($args['pattern']) || !is_string($args['pattern'])) {
            return false;
        }

        return (bool)preg_match($args['pattern'], $value);
    }

    public static function Equals($value, array $args): bool
    {
        if (!array_key_exists('value', $args)) {
            return false;
        }

        if ($args['strict'] ?? false) {
            return $value === $args['value'];
        }

        return $value == $args['value'];
    }
}