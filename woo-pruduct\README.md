# Woo Product Manager

WordPress eklentisi olarak geliştirilmiş olan **Woo Product Manager**, WooCommerce ürünlerini tarayarak Tutor LMS kurslarına bağlı olanları tespit eder ve sipariş durumuna göre buton görünürlüğünü kontrol eder.

## 🎯 Özellikler

### Ana İşlevler
- **Ürün Analizi**: WooCommerce ürünlerini tarayarak Tutor LMS kurslarına bağlı olanları tespit eder
- **Sipariş Durumu Kontrolü**: Tespit edilen her ürün için ilgili siparişlerin durumunu kontrol eder
- **Buton Görünürlük Kontrolü**: Sipariş durumuna göre "Sepete Ekle" butonunu gizler/gösterir
- **Otomatik Kayıt Kontrolü**: Kullanıcının kursa kayıtlı olup olmadığını kontrol eder
- **Kurs Bilgileri Sekmesi**: WooCommerce ürün sayfalarında detaylı kurs bilgileri gösterir

### Admin Panel Özellikleri
- **Dashboard**: Genel istatistikler ve hızlı erişim
- **Ürün Tarama**: Detaylı ürün analizi ve raporlama
- **Log Sistemi**: Tüm işlemlerin detaylı kaydı
- **Dışa Aktarma**: CSV formatında rapor dışa aktarma

## 📋 Gereksinimler

- WordPress 5.0 veya üzeri
- PHP 7.4 veya üzeri
- WooCommerce 5.0 veya üzeri
- Tutor LMS eklentisi

## 🚀 Kurulum

1. **Eklenti Dosyalarını Yükleyin**
   ```
   wp-content/plugins/woo-pruduct/
   ```

2. **WordPress Admin Panelinden Aktifleştirin**
   - Eklentiler > Yüklü Eklentiler
   - "Woo Product Manager" eklentisini aktifleştirin

3. **Gerekli İzinleri Kontrol Edin**
   - WooCommerce ve Tutor LMS eklentilerinin aktif olduğundan emin olun

## 🔧 Kullanım

### Dashboard
Admin panelinde **Woo Product** menüsünden ana sayfaya erişebilirsiniz. Burada:
- Genel istatistikleri görüntüleyebilirsiniz
- Hızlı işlemler yapabilirsiniz
- Son aktiviteleri takip edebilirsiniz
- Sistem durumunu kontrol edebilirsiniz

### Ürün Tarama
**Woo Product > Ürün Tarama** sayfasından:
1. "Taramayı Başlat" butonuna tıklayın
2. Sistem otomatik olarak tüm ürünleri tarayacak
3. Tutor LMS kurslarına bağlı ürünleri tespit edecek
4. Her ürün için sipariş istatistiklerini gösterecek
5. Sonuçları CSV formatında dışa aktarabilirsiniz

### Log İzleme
**Woo Product > Loglar** sayfasından:
- Tüm sipariş durumu değişikliklerini izleyebilirsiniz
- Buton durumu değişikliklerini görebilirsiniz
- Logları filtreleyebilirsiniz
- Logları CSV formatında dışa aktarabilirsiniz

## 🔄 Çalışma Mantığı

### Sipariş Durumu İzleme
Eklenti, WooCommerce'in `woocommerce_order_status_changed` hook'unu kullanarak:
1. Sipariş durumu değişikliklerini yakalar
2. Siparişin Tutor LMS kursuna ait olup olmadığını kontrol eder
3. Sipariş "completed" durumuna geçtiğinde kullanıcıyı kursa kaydeder
4. Buton görünürlüğünü günceller

### Buton Kontrolü
Ürün sayfalarında:
1. Kullanıcının kursa kayıtlı olup olmadığını kontrol eder
2. Kayıtlı ise "Sepete Ekle" butonunu gizler
3. Bunun yerine "Kursa Devam Et" butonu ve kayıt bilgilerini gösterir

### Kurs Bilgileri Sekmesi
WooCommerce ürün sayfalarında:
1. "Değerlendirmeler" sekmesinin yanına "Kurs Bilgileri" sekmesi ekler
2. Sadece Tutor LMS kurslarına bağlı ürünlerde görünür
3. Kurs detayları, istatistikler ve müfredatı gösterir
4. **Accordion müfredat**: Kurs konuları açılır-kapanır şeklinde
5. Responsive tasarım ile mobil uyumlu
6. Erişilebilirlik desteği (ARIA, klavye navigasyonu)

### Veri Yapısı
Eklenti şu meta key'leri kullanır:
- `_tutor_course_product_id`: Kurs-ürün ilişkisi
- `_tutor_product`: WooCommerce ürününün Tutor ürünü olduğunu belirtir

## 📊 Raporlama

### Dashboard İstatistikleri
- Tutor kurslarına bağlı ürün sayısı
- Toplam sipariş sayısı
- Tamamlanmış sipariş sayısı
- Tamamlanma oranı

### Tarama Sonuçları
Her ürün için:
- Kurs bilgileri
- Ürün bilgileri
- Fiyat bilgisi
- Sipariş istatistikleri
- Tamamlanma oranı
- Durum bilgisi

### Log Kayıtları
- Sipariş durumu değişiklikleri
- Buton durumu güncellemeleri
- Manuel işlemler
- Sistem aktiviteleri

## 🛠️ Geliştirici Notları

### Hook'lar ve Filter'lar
Eklenti şu WordPress hook'larını kullanır:
- `woocommerce_order_status_changed`: Sipariş durumu değişiklikleri
- `woocommerce_is_purchasable`: Ürün satın alınabilirlik kontrolü
- `woocommerce_single_product_summary`: Buton kontrolü

### Veritabanı Tabloları
Eklenti mevcut WordPress tablolarını kullanır:
- `wp_posts`: Kurs ve sipariş verileri
- `wp_postmeta`: Kurs-ürün ilişkileri
- `wp_options`: Ayarlar ve log verileri

### AJAX İşlemleri
- `woo_pruduct_scan`: Ürün tarama
- `woo_pruduct_toggle_button`: Buton durumu değiştirme
- `woo_pruduct_clear_logs`: Log temizleme

## 🔒 Güvenlik

- Tüm AJAX istekleri nonce ile korunur
- Kullanıcı yetkileri kontrol edilir
- Girdi verileri sanitize edilir
- SQL injection koruması vardır

## 📝 Changelog

### v1.0.0
- İlk sürüm
- Temel ürün tarama işlevi
- Sipariş durumu izleme
- Buton görünürlük kontrolü
- Admin panel arayüzü
- Log sistemi
- CSV dışa aktarma

## 🤝 Katkıda Bulunma

1. Bu repository'yi fork edin
2. Yeni bir feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add some amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Bir Pull Request oluşturun

## 📄 Lisans

Bu proje GPL v2 veya üzeri lisansı altında lisanslanmıştır.

## 🆘 Destek

Herhangi bir sorun yaşarsanız:
1. WordPress admin panelinde **Woo Product > Sistem Durumu** bölümünü kontrol edin
2. Log kayıtlarını inceleyin
3. Gerekli eklentilerin aktif olduğundan emin olun

## 📞 İletişim

Sorularınız için:
- GitHub Issues kullanın
- WordPress.org destek forumlarına başvurun

---

**Not**: Bu eklenti Tutor LMS ve WooCommerce eklentilerinin aktif olmasını gerektirir. Eklenti kurulumundan önce bu gereksinimlerin karşılandığından emin olun.
