<?php
/* ===========================================================================
 * Copyright 2020-2021 Zindex Software
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ============================================================================ */

namespace Opis\String\Exception;

use Throwable;

class InvalidCodePointException extends UnicodeException
{
    /**
     * @var mixed
     */
    protected $codePoint;

    /**
     * @param $codePoint
     * @param Throwable|null $previous
     */
    public function __construct($codePoint, Throwable $previous = null)
    {
        parent::__construct("Invalid code point", 0, $previous);
        $this->codePoint = $codePoint;
    }

    /**
     * @return mixed
     */
    public function codePoint()
    {
        return$this->codePoint;
    }
}
