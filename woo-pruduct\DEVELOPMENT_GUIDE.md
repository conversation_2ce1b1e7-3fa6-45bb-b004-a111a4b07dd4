# WooCommerce ve Tutor LMS Entegrasyonu - Geliştirme Rehberi

Bu dokümantasyon, WooCommerce ürünlerinin Tutor LMS kurslarıyla entegrasyonunu ve "Sepete Ekle" buton kontrolünü nasıl gerçekleştirdiğimizi detaylı olarak açıklar.

## 🎯 Proje Hedefi

**Ana Amaç**: WooCommerce ürünlerini tarayarak Tutor LMS kurslarına bağlı olanları tespit etmek ve sipariş durumuna göre "Sepete Ekle" butonunu kontrol etmek.

**Spesifik İhtiyaçlar**:
1. Kullanıcı kursa kayıtlı ise "Sepete Ekle" butonunu gizlemek
2. <PERSON><PERSON><PERSON> yerine "Kursa Devam Et" butonu göstermek
3. <PERSON><PERSON>t tarihini göstermek
4. Sipariş durumu değişikliklerini otomatik izlemek

## 🔍 Araştırma Aşaması

### 1. <PERSON><PERSON> LMS - WooCommerce İlişkisi Analizi

**Kritik Meta Key Keşfi**:
```php
// Tutor LMS'de kurs-ürün ilişkisi bu meta key ile saklanır
const TUTOR_COURSE_PRODUCT_ID_META = '_tutor_course_product_id';

// WooCommerce ürününün Tutor ürünü olduğunu belirten meta
const TUTOR_PRODUCT_META = '_tutor_product';
```

**Veritabanı Yapısı**:
```sql
-- wp_postmeta tablosunda kurs-ürün ilişkisi
SELECT post_id as course_id, meta_value as product_id 
FROM wp_postmeta 
WHERE meta_key = '_tutor_course_product_id'
```

### 2. Tutor LMS Hook'ları ve Fonksiyonları

**Önemli Fonksiyonlar**:
```php
// Kullanıcının kursa kayıtlı olup olmadığını kontrol eder
tutor_utils()->is_enrolled($course_id, $user_id)

// Ürünün hangi kursa ait olduğunu bulur
tutor_utils()->product_belongs_with_course($product_id)

// Kullanıcıyı kursa kaydeder
tutor_utils()->do_enroll($course_id, $order_id, $customer_id)
```

**Kritik Hook'lar**:
```php
// Sipariş durumu değişikliklerini yakalar
add_action('woocommerce_order_status_changed', 'callback', 10, 3);

// Kurs kayıt durumu değişikliklerini yakalar
add_action('tutor/course/enrol_status_change/after', 'callback', 10, 2);
```

## 🛠️ Teknik Implementasyon

### 1. WooCommerce Buton Kontrolü

**Ana Strateji**: WooCommerce'in hook sistemini kullanarak buton görünürlüğünü kontrol etmek.

#### A) Ürün Satın Alınabilirlik Kontrolü
```php
/**
 * WooCommerce'in is_purchasable fonksiyonunu override ederek
 * kayıtlı kullanıcılar için ürünü satın alınamaz hale getiriyoruz
 */
add_filter('woocommerce_is_purchasable', array($this, 'control_product_purchasability'), 10, 2);

public function control_product_purchasability($is_purchasable, $product) {
    if (!$is_purchasable) {
        return $is_purchasable;
    }
    
    $product_id = $product->get_id();
    $course_id = $this->get_course_by_product($product_id);
    
    if ($course_id) {
        $user_id = get_current_user_id();
        if ($user_id && $this->is_user_enrolled_in_course($user_id, $course_id)) {
            return false; // Kullanıcı zaten kayıtlı, satın alınamaz
        }
    }
    
    return $is_purchasable;
}
```

#### B) Sepete Ekle Butonunu Değiştirme
```php
/**
 * WooCommerce'in single product summary hook'unu kullanarak
 * sepete ekle butonunu kaldırıp özel mesaj ekliyoruz
 */
add_action('woocommerce_single_product_summary', array($this, 'control_add_to_cart_button'), 25);

public function control_add_to_cart_button() {
    global $product;
    
    if (!$product) {
        return;
    }
    
    $product_id = $product->get_id();
    $course_id = $this->get_course_by_product($product_id);
    
    if ($course_id) {
        $user_id = get_current_user_id();
        if ($user_id && $this->is_user_enrolled_in_course($user_id, $course_id)) {
            // Orijinal butonu kaldır
            remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
            // Özel mesajı ekle
            add_action('woocommerce_single_product_summary', array($this, 'show_enrolled_message'), 30);
        }
    }
}
```

#### C) Özel Kayıt Mesajı Gösterme
```php
public function show_enrolled_message() {
    global $product;
    $course_id = $this->get_course_by_product($product->get_id());
    $course_url = get_permalink($course_id);
    $enrollment_date = $this->get_user_enrollment_date(get_current_user_id(), $course_id);
    
    echo '<div class="woo-pruduct-enrolled-message">';
    echo '<p class="enrolled-status"><span class="dashicons dashicons-yes-alt"></span> Bu kursa zaten kayıtlısınız!</p>';
    if ($enrollment_date) {
        echo '<p class="enrollment-date">Kayıt tarihi: ' . $enrollment_date . '</p>';
    }
    echo '<a href="' . esc_url($course_url) . '" class="button alt">Kursa Devam Et</a>';
    echo '</div>';
    
    // CSS stilleri inline olarak ekliyoruz
    wp_add_inline_style('woocommerce-general', '
        .woo-pruduct-enrolled-message {
            background: #f0f8ff;
            border: 2px solid #0073aa;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        /* ... diğer CSS kuralları ... */
    ');
}
```

### 2. Sipariş Durumu İzleme

**Ana Hook**: `woocommerce_order_status_changed`

```php
add_action('woocommerce_order_status_changed', array($this, 'handle_order_status_change'), 10, 3);

public function handle_order_status_change($order_id, $old_status, $new_status) {
    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }
    
    // Sipariş öğelerini kontrol et
    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $course_id = $this->get_course_by_product($product_id);
        
        if ($course_id) {
            // Bu bir Tutor LMS kursu siparişi
            $this->log_order_status_change($order_id, $product_id, $course_id, $old_status, $new_status);
            
            // Sipariş tamamlandıysa buton durumunu güncelle
            if ($new_status === 'completed') {
                $this->update_button_visibility($product_id, $course_id, $new_status);
            }
        }
    }
}
```

### 3. Kurs-Ürün İlişkisi Bulma

**Kritik Fonksiyon**:
```php
private function get_course_by_product($product_id) {
    global $wpdb;
    
    $course_id = $wpdb->get_var($wpdb->prepare(
        "SELECT post_id FROM {$wpdb->postmeta} 
         WHERE meta_key = '_tutor_course_product_id' 
         AND meta_value = %d",
        $product_id
    ));
    
    return $course_id ? intval($course_id) : false;
}
```

### 4. Kullanıcı Kayıt Durumu Kontrolü

**İki Yöntem Kullanıyoruz**:

#### A) Tutor LMS Fonksiyonu (Tercih Edilen)
```php
private function is_user_enrolled_in_course($user_id, $course_id) {
    if (function_exists('tutor_utils')) {
        return tutor_utils()->is_enrolled($course_id, $user_id);
    }
    
    // Fallback yöntemi...
}
```

#### B) Manuel Veritabanı Kontrolü (Fallback)
```php
// Fallback: Manuel kontrol
global $wpdb;
$enrolled = $wpdb->get_var($wpdb->prepare(
    "SELECT ID FROM {$wpdb->posts} 
     WHERE post_type = 'tutor_enrolled' 
     AND post_parent = %d 
     AND post_author = %d 
     AND post_status = 'completed'",
    $course_id,
    $user_id
));

return !empty($enrolled);
```

## 🔧 Hook Sistemi ve Öncelikler

### WooCommerce Hook Öncelikleri

```php
// Buton kontrolü için kritik hook sıralaması
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_title', 5);
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10);
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_price', 10);
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20);
add_action('woocommerce_single_product_summary', array($this, 'control_add_to_cart_button'), 25); // BİZİM HOOK
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
```

**Neden 25 Önceliği?**: 
- Orijinal buton (öncelik 30) yüklenmeden önce kontrol etmemiz gerekiyor
- Fiyat (öncelik 10) gösterildikten sonra kontrol etmek mantıklı

### Filter Hook'ları

```php
// Ürün satın alınabilirlik kontrolü
add_filter('woocommerce_is_purchasable', array($this, 'control_product_purchasability'), 10, 2);

// Sepet URL'si kontrolü (opsiyonel)
add_filter('woocommerce_cart_item_permalink', array($this, 'tutor_update_product_url'), 10, 2);
```

## 📊 Veri Akışı ve Mantık

### 1. Sayfa Yüklenme Süreci

```
1. Kullanıcı ürün sayfasını ziyaret eder
   ↓
2. WooCommerce ürün bilgilerini yükler
   ↓
3. Bizim hook'umuz devreye girer (öncelik 25)
   ↓
4. Ürün ID'sinden kurs ID'sini buluruz
   ↓
5. Kullanıcının kayıt durumunu kontrol ederiz
   ↓
6. Kayıtlı ise: Orijinal butonu kaldırır, özel mesaj gösteririz
   Kayıtlı değilse: Normal akış devam eder
```

### 2. Sipariş Tamamlanma Süreci

```
1. Kullanıcı siparişi tamamlar
   ↓
2. WooCommerce sipariş durumunu 'completed' yapar
   ↓
3. woocommerce_order_status_changed hook'u tetiklenir
   ↓
4. Bizim fonksiyonumuz çalışır
   ↓
5. Sipariş öğelerini kontrol ederiz
   ↓
6. Tutor LMS kursu varsa log kaydı yaparız
   ↓
7. Kullanıcı bir sonraki ziyarette yeni arayüzü görür
```

## 🎨 CSS ve JavaScript Entegrasyonu

### CSS Enjeksiyonu

```php
// wp_add_inline_style kullanarak dinamik CSS ekleme
wp_add_inline_style('woocommerce-general', '
    .woo-pruduct-enrolled-message {
        background: #f0f8ff;
        border: 2px solid #0073aa;
        border-radius: 5px;
        padding: 20px;
        margin: 20px 0;
        text-align: center;
    }
    .woo-pruduct-enrolled-message .enrolled-status {
        color: #0073aa;
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 10px;
    }
    .woo-pruduct-enrolled-message .dashicons {
        color: #46b450;
    }
');
```

**Neden Inline CSS?**: 
- Sadece gerekli sayfalarda yüklenir
- Ek dosya yüklemesi gerektirmez
- Dinamik içerik için uygun

## 🔍 Debug ve Test Yöntemleri

### 1. Log Sistemi

```php
private function log_order_status_change($order_id, $product_id, $course_id, $old_status, $new_status) {
    $log_entry = array(
        'timestamp' => current_time('mysql'),
        'order_id' => $order_id,
        'product_id' => $product_id,
        'course_id' => $course_id,
        'old_status' => $old_status,
        'new_status' => $new_status
    );
    
    $logs = get_option('woo_pruduct_logs', array());
    $logs[] = $log_entry;
    
    // Son 1000 log kaydını tut
    if (count($logs) > 1000) {
        $logs = array_slice($logs, -1000);
    }
    
    update_option('woo_pruduct_logs', $logs);
}
```

### 2. Test Senaryoları

**Test Case 1: Kayıtlı Kullanıcı**
```php
// Test: Kullanıcı kursa kayıtlı mı?
$user_id = 1;
$course_id = 123;
$is_enrolled = tutor_utils()->is_enrolled($course_id, $user_id);
// Beklenen: true ise buton gizlenmeli
```

**Test Case 2: Sipariş Durumu Değişikliği**
```php
// Test: Sipariş completed olduğunda log kaydı yapılıyor mu?
$order_id = 456;
do_action('woocommerce_order_status_changed', $order_id, 'processing', 'completed');
// Beklenen: Log tablosunda yeni kayıt
```

## 🚨 Kritik Noktalar ve Dikkat Edilmesi Gerekenler

### 1. Hook Öncelikleri
- **ÖNEMLİ**: Hook önceliklerini doğru ayarlamak kritik
- WooCommerce'in varsayılan hook'larından önce çalışmalı
- Çakışmaları önlemek için test edilmeli

### 2. Performans Optimizasyonu
```php
// Cache kullanımı
$cache_key = "tutor_is_enrolled_{$course_id}_{$user_id}";
$get_enrolled_info = TutorCache::get($cache_key);
if (!$get_enrolled_info) {
    // Veritabanı sorgusu
    TutorCache::set($cache_key, $get_enrolled_info);
}
```

### 3. Güvenlik Önlemleri
```php
// Nonce kontrolü
check_ajax_referer('woo_pruduct_nonce', 'nonce');

// Yetki kontrolü
if (!current_user_can('manage_options')) {
    wp_die(__('Yetkiniz yok', 'woo-pruduct'));
}

// Veri sanitizasyonu
$product_id = intval($_POST['product_id']);
$action = sanitize_text_field($_POST['action_type']);
```

### 4. Uyumluluk Kontrolleri
```php
// Gerekli eklentilerin varlığını kontrol et
if (!class_exists('WooCommerce')) {
    // Hata mesajı göster
}

if (!function_exists('tutor_utils')) {
    // Hata mesajı göster
}
```

## 📋 Implementasyon Checklist

### Temel Gereksinimler
- [ ] WooCommerce hook'larını doğru kullanmak
- [ ] Tutor LMS fonksiyonlarını entegre etmek
- [ ] Veritabanı sorgularını optimize etmek
- [ ] Güvenlik önlemlerini almak

### UI/UX Gereksinimleri
- [ ] "Sepete Ekle" butonunu gizlemek
- [ ] "Kursa Devam Et" butonu eklemek
- [ ] Kayıt tarihini göstermek
- [ ] Responsive tasarım sağlamak

### Admin Panel Gereksinimleri
- [ ] Ürün tarama sistemi
- [ ] Log görüntüleme
- [ ] İstatistik dashboard'u
- [ ] CSV dışa aktarma

### Test Gereksinimleri
- [ ] Kayıtlı kullanıcı testi
- [ ] Kayıtsız kullanıcı testi
- [ ] Sipariş durumu değişiklik testi
- [ ] Performans testi

## 🔄 Alternatif Yaklaşımlar ve Gelişmiş Teknikler

### 1. JavaScript ile Dinamik Buton Kontrolü

Bazı durumlarda PHP hook'ları yeterli olmayabilir. Bu durumda JavaScript ile dinamik kontrol yapılabilir:

```javascript
// AJAX ile kullanıcı durumunu kontrol et
jQuery(document).ready(function($) {
    const productId = $('input[name="add-to-cart"]').val();

    if (productId) {
        $.ajax({
            url: woocommerce_params.ajax_url,
            type: 'POST',
            data: {
                action: 'check_user_enrollment',
                product_id: productId,
                nonce: woo_pruduct_nonce
            },
            success: function(response) {
                if (response.success && response.data.is_enrolled) {
                    // Butonu değiştir
                    $('.single_add_to_cart_button').replaceWith(response.data.custom_button);
                }
            }
        });
    }
});
```

### 2. Shortcode Yaklaşımı

Tema dosyalarını değiştirmeden buton kontrolü:

```php
// Shortcode ile özel buton
add_shortcode('tutor_course_button', array($this, 'render_course_button'));

public function render_course_button($atts) {
    $atts = shortcode_atts(array(
        'product_id' => 0,
        'course_id' => 0
    ), $atts);

    $user_id = get_current_user_id();
    $is_enrolled = $this->is_user_enrolled_in_course($user_id, $atts['course_id']);

    if ($is_enrolled) {
        return $this->get_enrolled_button_html($atts['course_id']);
    } else {
        return $this->get_purchase_button_html($atts['product_id']);
    }
}
```

### 3. REST API Entegrasyonu

Modern yaklaşım için REST API kullanımı:

```php
// REST API endpoint'i kaydet
add_action('rest_api_init', array($this, 'register_rest_routes'));

public function register_rest_routes() {
    register_rest_route('woo-pruduct/v1', '/check-enrollment/(?P<product_id>\d+)', array(
        'methods' => 'GET',
        'callback' => array($this, 'check_enrollment_api'),
        'permission_callback' => array($this, 'check_api_permissions')
    ));
}

public function check_enrollment_api($request) {
    $product_id = $request['product_id'];
    $course_id = $this->get_course_by_product($product_id);
    $user_id = get_current_user_id();

    $is_enrolled = $this->is_user_enrolled_in_course($user_id, $course_id);

    return new WP_REST_Response(array(
        'is_enrolled' => $is_enrolled,
        'course_id' => $course_id,
        'enrollment_date' => $is_enrolled ? $this->get_user_enrollment_date($user_id, $course_id) : null
    ), 200);
}
```

## 🎯 Proje Özeti ve Başarı Faktörleri

### Başarılı Olan Yaklaşımlar

1. **Hook Sistemi Kullanımı**: WooCommerce'in mevcut hook sistemini kullanarak minimal müdahale
2. **Tutor LMS Fonksiyonları**: Mevcut Tutor LMS fonksiyonlarını kullanarak uyumluluk sağlama
3. **Fallback Mekanizmaları**: Tutor LMS fonksiyonları çalışmazsa manuel veritabanı kontrolü
4. **Kapsamlı Log Sistemi**: Tüm işlemleri kaydetme ve debug kolaylığı
5. **Admin Panel Entegrasyonu**: Kullanıcı dostu yönetim arayüzü

### Kritik Başarı Faktörleri

1. **Doğru Hook Seçimi**: `woocommerce_single_product_summary` hook'u ile doğru zamanda müdahale
2. **Öncelik Yönetimi**: Hook önceliklerini doğru ayarlayarak çakışmaları önleme
3. **Veritabanı Optimizasyonu**: Cache kullanımı ve efficient sorgular
4. **Güvenlik**: Nonce, sanitization ve yetki kontrolleri
5. **Uyumluluk**: Farklı tema ve eklenti kombinasyonlarında çalışma

## 📑 WooCommerce Ürün Sekmelerine "Kurs Bilgileri" Ekleme

### Gereksinim Analizi

**Hedef**: WooCommerce ürün sayfalarında "Değerlendirmeler" sekmesinin yanına "Kurs Bilgileri" sekmesi eklemek.

**Koşul**: Bu sekme sadece Tutor LMS kurslarına bağlı ürünlerde görünmeli.

### Teknik Implementasyon

#### 1. WooCommerce Sekme Sistemi

WooCommerce, ürün sayfalarında sekme sistemi için `woocommerce_product_tabs` filter'ını kullanır:

```php
/**
 * WooCommerce ürün sekmelerine yeni sekme ekleme
 */
add_filter('woocommerce_product_tabs', array($this, 'add_course_info_tab'), 25);

public function add_course_info_tab($tabs) {
    global $product;

    if (!$product) {
        return $tabs;
    }

    $product_id = $product->get_id();
    $course_id = $this->get_course_by_product($product_id);

    // Sadece Tutor LMS kurslarına bağlı ürünlerde sekmeyi göster
    if ($course_id) {
        $tabs['course_info'] = array(
            'title'    => __('Kurs Bilgileri', 'woo-pruduct'),
            'priority' => 25, // Değerlendirmeler sekmesinden sonra
            'callback' => array($this, 'course_info_tab_content')
        );
    }

    return $tabs;
}
```

#### 2. Sekme İçeriği Callback'i

```php
/**
 * Sekme içeriği için callback fonksiyonu
 */
add_action('woocommerce_product_tab_course_info', array($this, 'course_info_tab_content'));

public function course_info_tab_content() {
    global $product;

    $product_id = $product->get_id();
    $course_id = $this->get_course_by_product($product_id);

    if (!$course_id) {
        echo '<p>' . __('Bu ürün için kurs bilgisi bulunamadı.', 'woo-pruduct') . '</p>';
        return;
    }

    // Kurs bilgilerini al ve göster
    $course = get_post($course_id);
    $course_meta = $this->get_course_meta_info($course_id);
    $course_stats = $this->get_course_statistics($course_id);
    $course_curriculum = $this->get_course_curriculum($course_id);

    $this->render_course_info_template($course, $course_meta, $course_stats, $course_curriculum);
}
```

#### 3. Kurs Meta Bilgilerini Alma

```php
private function get_course_meta_info($course_id) {
    $meta = array();

    // Tutor LMS'in standart meta key'lerini kullan
    $meta_keys = array(
        'level' => '_tutor_course_level',
        'duration' => '_course_duration',
        'max_students' => '_tutor_course_max_students',
        'language' => '_tutor_course_language',
        'requirements' => '_tutor_course_requirements',
        'target_audience' => '_tutor_course_target_audience',
        'benefits' => '_tutor_course_benefits'
    );

    foreach ($meta_keys as $key => $meta_key) {
        $value = get_post_meta($course_id, $meta_key, true);
        if ($value) {
            $meta[$key] = $value;
        }
    }

    return $meta;
}
```

#### 4. Kurs İstatistiklerini Alma

```php
private function get_course_statistics($course_id) {
    global $wpdb;

    $stats = array();

    // Kayıtlı öğrenci sayısı
    $total_students = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->posts}
         WHERE post_type = 'tutor_enrolled'
         AND post_parent = %d
         AND post_status = 'completed'",
        $course_id
    ));
    $stats['total_students'] = intval($total_students);

    // Ders sayısı
    $total_lessons = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->posts}
         WHERE post_type = 'lesson'
         AND post_parent = %d
         AND post_status = 'publish'",
        $course_id
    ));
    $stats['total_lessons'] = intval($total_lessons);

    // Quiz sayısı
    $total_quizzes = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->posts}
         WHERE post_type = 'tutor_quiz'
         AND post_parent = %d
         AND post_status = 'publish'",
        $course_id
    ));
    $stats['total_quizzes'] = intval($total_quizzes);

    // Tutor LMS rating sistemi
    if (function_exists('tutor_utils')) {
        $rating = tutor_utils()->get_course_rating($course_id);
        $stats['average_rating'] = $rating->rating_avg ?? 0;
        $stats['rating_count'] = $rating->rating_count ?? 0;
    }

    return $stats;
}
```

#### 5. Kurs Müfredatını Alma

```php
private function get_course_curriculum($course_id) {
    global $wpdb;

    $curriculum = array();

    // Tutor LMS'de kurs yapısı: Course -> Topics -> Lessons/Quizzes
    $topics = $wpdb->get_results($wpdb->prepare(
        "SELECT ID, post_title, menu_order
         FROM {$wpdb->posts}
         WHERE post_type = 'topics'
         AND post_parent = %d
         AND post_status = 'publish'
         ORDER BY menu_order ASC",
        $course_id
    ));

    foreach ($topics as $topic) {
        $topic_data = array(
            'id' => $topic->ID,
            'title' => $topic->post_title,
            'lessons' => array(),
            'quizzes' => array()
        );

        // Topic'e ait dersleri al
        $lessons = $wpdb->get_results($wpdb->prepare(
            "SELECT ID, post_title
             FROM {$wpdb->posts}
             WHERE post_type = 'lesson'
             AND post_parent = %d
             AND post_status = 'publish'
             ORDER BY menu_order ASC",
            $topic->ID
        ));

        foreach ($lessons as $lesson) {
            $topic_data['lessons'][] = array(
                'id' => $lesson->ID,
                'title' => $lesson->post_title
            );
        }

        // Topic'e ait quizleri al
        $quizzes = $wpdb->get_results($wpdb->prepare(
            "SELECT ID, post_title
             FROM {$wpdb->posts}
             WHERE post_type = 'tutor_quiz'
             AND post_parent = %d
             AND post_status = 'publish'
             ORDER BY menu_order ASC",
            $topic->ID
        ));

        foreach ($quizzes as $quiz) {
            $topic_data['quizzes'][] = array(
                'id' => $quiz->ID,
                'title' => $quiz->post_title
            );
        }

        $curriculum[] = $topic_data;
    }

    return $curriculum;
}
```

### Sekme Öncelikleri ve Sıralama

WooCommerce'in varsayılan sekme öncelikleri:
- **Description**: 10
- **Additional Information**: 20
- **Reviews**: 30

Bizim "Kurs Bilgileri" sekmemiz için **25** önceliği seçtik, böylece:
- Description (10)
- Additional Information (20)
- **Kurs Bilgileri (25)** ← Bizim sekme
- Reviews (30)

### CSS Styling Stratejisi

Sekme içeriği için inline CSS kullanıyoruz çünkü:
1. Sadece gerekli sayfalarda yüklenir
2. Ek dosya yüklemesi gerektirmez
3. Tema bağımsız çalışır

```php
// Template fonksiyonunun sonunda CSS ekliyoruz
echo '<style>
.woo-pruduct-course-info {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    line-height: 1.6;
}
/* ... diğer CSS kuralları ... */
</style>';
```

### Accordion (Açılır-Kapanır) Müfredat Sistemi

#### HTML Yapısı
```html
<div class="curriculum-accordion">
    <div class="curriculum-topic">
        <div class="topic-header" data-topic-id="0">
            <h4 class="topic-title">
                <span class="dashicons dashicons-folder"></span>
                Konu Başlığı
                <span class="topic-count">(5 öğe)</span>
            </h4>
            <span class="accordion-toggle">
                <span class="dashicons dashicons-arrow-down-alt2"></span>
            </span>
        </div>
        <div class="topic-content" id="topic-content-0">
            <!-- Ders ve quiz listesi -->
        </div>
    </div>
</div>
```

#### CSS Animasyonları
```css
.topic-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.topic-content.active {
    max-height: 1000px;
}

.accordion-toggle {
    transition: transform 0.3s ease;
}

.topic-header.active .accordion-toggle {
    transform: rotate(180deg);
}
```

#### JavaScript İşlevselliği
```javascript
$('.topic-header').on('click', function() {
    const $header = $(this);
    const $content = $header.next('.topic-content');
    const $accordion = $header.closest('.curriculum-accordion');

    // Diğer açık konuları kapat (accordion davranışı)
    $accordion.find('.topic-header.active').not($header).each(function() {
        $(this).removeClass('active');
        $(this).next('.topic-content').removeClass('active');
    });

    // Mevcut konuyu aç/kapat
    $header.toggleClass('active');
    $content.toggleClass('active');
});
```

#### Erişilebilirlik (Accessibility)
```javascript
// ARIA attributes
$header.attr({
    'role': 'button',
    'tabindex': '0',
    'aria-expanded': 'false',
    'aria-controls': contentId
});

// Klavye desteği
$('.topic-header').on('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        $(this).click();
    }
});
```

### Responsive Tasarım

```css
@media (max-width: 768px) {
    .meta-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 24px;
    }
}
```

### Gelecek Geliştirmeler İçin Öneriler

1. **Multisite Desteği**: WordPress multisite ağları için optimizasyon
2. **Çoklu Dil Desteği**: WPML/Polylang entegrasyonu
3. **Gelişmiş Cache**: Redis/Memcached desteği
4. **Webhook Entegrasyonu**: Harici sistemlerle entegrasyon
5. **A/B Test Desteği**: Farklı buton tasarımları test etme
6. **Kurs Önizleme**: Sekme içinde video önizleme özelliği
7. **Etkileşimli Müfredat**: Tıklanabilir ders listesi

## 📚 Referanslar ve Kaynaklar

### WooCommerce Hook Referansları
- [WooCommerce Action Reference](https://woocommerce.github.io/code-reference/hooks/hooks.html)
- [WooCommerce Template Structure](https://woocommerce.com/document/template-structure/)

### Tutor LMS Geliştirici Kaynakları
- [Tutor LMS Hook Reference](https://docs.themeum.com/tutor-lms/)
- [Tutor LMS Database Schema](https://docs.themeum.com/tutor-lms/developer-guide/)

### WordPress Geliştirme
- [WordPress Plugin API](https://developer.wordpress.org/plugins/)
- [WordPress Database API](https://developer.wordpress.org/apis/handbook/database/)

Bu rehber, benzer bir projeyi sıfırdan geliştirmek için gereken tüm teknik detayları içermektedir. Her adım test edilmiş ve çalışır durumda olan kodlarla desteklenmiştir.
