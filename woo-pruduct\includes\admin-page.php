<?php
/**
 * Ana admin sayfası
 */

// <PERSON><PERSON>rudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Genel istatistikleri al
global $wpdb;

// <PERSON><PERSON> k<PERSON>na bağlı ürün sayısı
$tutor_products_count = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->postmeta} pm
    LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
    WHERE pm.meta_key = '_tutor_course_product_id'
    AND p.post_status = 'publish'
    AND p.post_type = 'courses'
");

// Toplam sipariş sayısı (Tutor ürünleri için)
$total_orders = $wpdb->get_var("
    SELECT COUNT(DISTINCT p.ID)
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
    INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
    INNER JOIN {$wpdb->postmeta} pm ON oim.meta_value = pm.meta_value
    WHERE p.post_type = 'shop_order'
    AND oim.meta_key = '_product_id'
    AND pm.meta_key = '_tutor_course_product_id'
");

// Tamamlanmış sipariş sayısı
$completed_orders = $wpdb->get_var("
    SELECT COUNT(DISTINCT p.ID)
    FROM {$wpdb->posts} p
    INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
    INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
    INNER JOIN {$wpdb->postmeta} pm ON oim.meta_value = pm.meta_value
    WHERE p.post_type = 'shop_order'
    AND p.post_status = 'wc-completed'
    AND oim.meta_key = '_product_id'
    AND pm.meta_key = '_tutor_course_product_id'
");

// Son log kayıtları
$recent_logs = get_option('woo_pruduct_logs', array());
$recent_logs = array_slice(array_reverse($recent_logs), 0, 5);

?>

<div class="wrap">
    <h1><?php _e('Woo Product Manager', 'woo-pruduct'); ?></h1>
    
    <div class="woo-pruduct-dashboard">
        <!-- İstatistik Kartları -->
        <div class="woo-pruduct-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <span class="dashicons dashicons-products"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo esc_html($tutor_products_count); ?></h3>
                    <p><?php _e('Tutor Kurslarına Bağlı Ürün', 'woo-pruduct'); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <span class="dashicons dashicons-cart"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo esc_html($total_orders); ?></h3>
                    <p><?php _e('Toplam Sipariş', 'woo-pruduct'); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo esc_html($completed_orders); ?></h3>
                    <p><?php _e('Tamamlanmış Sipariş', 'woo-pruduct'); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="stat-content">
                    <h3><?php echo $total_orders > 0 ? round(($completed_orders / $total_orders) * 100, 1) : 0; ?>%</h3>
                    <p><?php _e('Tamamlanma Oranı', 'woo-pruduct'); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Hızlı İşlemler -->
        <div class="woo-pruduct-quick-actions">
            <h2><?php _e('Hızlı İşlemler', 'woo-pruduct'); ?></h2>
            <div class="quick-actions-grid">
                <a href="<?php echo admin_url('admin.php?page=woo-pruduct-scan'); ?>" class="quick-action-btn scan-btn">
                    <span class="dashicons dashicons-search"></span>
                    <?php _e('Ürün Tarama', 'woo-pruduct'); ?>
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=woo-pruduct-logs'); ?>" class="quick-action-btn logs-btn">
                    <span class="dashicons dashicons-list-view"></span>
                    <?php _e('Logları Görüntüle', 'woo-pruduct'); ?>
                </a>
                
                <button id="refresh-stats" class="quick-action-btn refresh-btn">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('İstatistikleri Yenile', 'woo-pruduct'); ?>
                </button>
                
                <button id="clear-logs" class="quick-action-btn clear-btn">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('Logları Temizle', 'woo-pruduct'); ?>
                </button>
            </div>
        </div>
        
        <!-- Son Aktiviteler -->
        <div class="woo-pruduct-recent-activity">
            <h2><?php _e('Son Aktiviteler', 'woo-pruduct'); ?></h2>
            <?php if (!empty($recent_logs)) : ?>
                <div class="activity-list">
                    <?php foreach ($recent_logs as $log) : ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <?php if ($log['new_status'] === 'completed') : ?>
                                    <span class="dashicons dashicons-yes-alt" style="color: #46b450;"></span>
                                <?php else : ?>
                                    <span class="dashicons dashicons-clock" style="color: #ffb900;"></span>
                                <?php endif; ?>
                            </div>
                            <div class="activity-content">
                                <p>
                                    <strong><?php _e('Sipariş', 'woo-pruduct'); ?> #<?php echo esc_html($log['order_id']); ?></strong>
                                    <?php echo sprintf(
                                        __('durumu %s → %s olarak değişti', 'woo-pruduct'),
                                        '<span class="status-badge status-' . esc_attr($log['old_status']) . '">' . esc_html($log['old_status']) . '</span>',
                                        '<span class="status-badge status-' . esc_attr($log['new_status']) . '">' . esc_html($log['new_status']) . '</span>'
                                    ); ?>
                                </p>
                                <small class="activity-meta">
                                    <?php echo sprintf(
                                        __('Ürün: %s | Kurs: %s | %s', 'woo-pruduct'),
                                        esc_html($log['product_id']),
                                        esc_html($log['course_id']),
                                        esc_html($log['timestamp'])
                                    ); ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <div class="no-activity">
                    <span class="dashicons dashicons-info"></span>
                    <p><?php _e('Henüz aktivite kaydı bulunmuyor.', 'woo-pruduct'); ?></p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Sistem Durumu -->
        <div class="woo-pruduct-system-status">
            <h2><?php _e('Sistem Durumu', 'woo-pruduct'); ?></h2>
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-label"><?php _e('WooCommerce:', 'woo-pruduct'); ?></span>
                    <span class="status-value <?php echo class_exists('WooCommerce') ? 'status-ok' : 'status-error'; ?>">
                        <?php echo class_exists('WooCommerce') ? __('Aktif', 'woo-pruduct') : __('Pasif', 'woo-pruduct'); ?>
                    </span>
                </div>
                
                <div class="status-item">
                    <span class="status-label"><?php _e('Tutor LMS:', 'woo-pruduct'); ?></span>
                    <span class="status-value <?php echo function_exists('tutor_utils') ? 'status-ok' : 'status-error'; ?>">
                        <?php echo function_exists('tutor_utils') ? __('Aktif', 'woo-pruduct') : __('Pasif', 'woo-pruduct'); ?>
                    </span>
                </div>
                
                <div class="status-item">
                    <span class="status-label"><?php _e('PHP Sürümü:', 'woo-pruduct'); ?></span>
                    <span class="status-value <?php echo version_compare(PHP_VERSION, '7.4', '>=') ? 'status-ok' : 'status-warning'; ?>">
                        <?php echo PHP_VERSION; ?>
                    </span>
                </div>
                
                <div class="status-item">
                    <span class="status-label"><?php _e('WordPress Sürümü:', 'woo-pruduct'); ?></span>
                    <span class="status-value status-ok">
                        <?php echo get_bloginfo('version'); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.woo-pruduct-dashboard {
    max-width: 1200px;
}

.woo-pruduct-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-icon {
    margin-right: 15px;
}

.stat-icon .dashicons {
    font-size: 40px;
    color: #0073aa;
}

.stat-content h3 {
    margin: 0;
    font-size: 32px;
    font-weight: bold;
    color: #333;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 14px;
}

.woo-pruduct-quick-actions,
.woo-pruduct-recent-activity,
.woo-pruduct-system-status {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border: 2px solid #0073aa;
    border-radius: 6px;
    text-decoration: none;
    color: #0073aa;
    background: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.quick-action-btn:hover {
    background: #0073aa;
    color: #fff;
    text-decoration: none;
}

.quick-action-btn .dashicons {
    margin-right: 8px;
}

.activity-list {
    margin-top: 15px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    margin-right: 15px;
    margin-top: 2px;
}

.activity-content p {
    margin: 0 0 5px 0;
}

.activity-meta {
    color: #666;
    font-size: 12px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-completed {
    background: #46b450;
    color: #fff;
}

.status-pending {
    background: #ffb900;
    color: #fff;
}

.status-processing {
    background: #00a0d2;
    color: #fff;
}

.no-activity {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-activity .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.status-label {
    font-weight: 500;
}

.status-value {
    font-weight: bold;
}

.status-ok {
    color: #46b450;
}

.status-warning {
    color: #ffb900;
}

.status-error {
    color: #dc3232;
}
</style>

<script>
jQuery(document).ready(function($) {
    $('#refresh-stats').on('click', function() {
        location.reload();
    });
    
    $('#clear-logs').on('click', function() {
        if (confirm('<?php _e("Tüm logları silmek istediğinizden emin misiniz?", "woo-pruduct"); ?>')) {
            $.post(ajaxurl, {
                action: 'woo_pruduct_clear_logs',
                nonce: '<?php echo wp_create_nonce("woo_pruduct_nonce"); ?>'
            }, function(response) {
                if (response.success) {
                    location.reload();
                }
            });
        }
    });
});
</script>
